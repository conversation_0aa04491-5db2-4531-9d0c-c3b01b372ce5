
/*++

    Copyright (c) 2002 Microsoft Corporation

    Module Name:

        sbe.idl

    Abstract:

        This module the StreamBuffer interface definitions & CLSIDs, public

--*/

import "unknwn.idl" ;
import "wtypes.idl" ;
import "objidl.idl";
import "strmif.idl" ;

//  ============================================================================

//  interfaces
interface   IStreamBufferSink ;                     //  get recording objects
interface   IStreamBufferSource ;                   //  associates with IStreamBufferSink
interface   IStreamBufferRecordControl ;            //  recording control
interface   IStreamBufferRecordingAttribute ;       //  StreamBuffer attribute creation
interface   IEnumStreamBufferRecordingAttrib ;      //  StreamBuffer attribute enumeration
interface   IStreamBufferConfigure ;                //  configuration interface
interface   IStreamBufferMediaSeeking ;             //  IMediaSeeking but with different GUID
interface   IStreamBufferPolicy ;                   //  StreamBuffer policies
interface   IStreamBufferInitialize ;               //  allows 3rd party app to set HKEY

[
    object,
    uuid(9ce50f2d-6ba7-40fb-a034-50b1a674ec78),
    pointer_default(unique)
]
[local] interface IStreamBufferInitialize : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        SetHKEY ()

        Implemented on StreamBufferStreamSink and StreamBufferSource filters.
        Gives a hosting application the ability to specify HKEY root in
          registry.  This method must called **early**: after the filter is
          instantiated, but before StreamBufferSource is locked (explicitly or
          implicitely) if calling the method on StreamBufferSource, or before
          a source is set (via IStreamBufferSource or IFileSourceFilter) if
          calling the method on StreamBufferStreamSource.  If a call is made
          after either filter has been initialized internally, the call will
          fail with E_UNEXPECTED.  The hosting application is responsible for
          ensuring that the HKEY passed in is writable & readable per the
          logged-on user privileges.  The HKEY is duplicated internally,
          so the caller can close it after making this call.
    --*/
    HRESULT
    SetHKEY (
        [in]    HKEY    hkeyRoot
        ) ;

    /*++
        ------------------------------------------------------------------------
        SetSIDs ()

        Implemented on StreamBufferStreamSink and StreamBufferSource filters.
        Provides a way for the hosting application to specify security-level
          sharing between capture and render processes and contexts.  By
          default security attributes are inherited from the hosting process,
          unless the application overrides the defaults and provides them via
          this method.
    --*/
    HRESULT
    SetSIDs (
        [in]                    DWORD   cSIDs,
        [in, size_is (cSIDs)]   PSID *  ppSID
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferSink

    Stream Source interface;
    implemented on the StreamBufferSink filter;
    Only way to get a recorder object's IUnknown (object will subsequently
        be associated with this Sink)
--*/

enum {
    RECORDING_TYPE_CONTENT = 0,         //  no post-recording or overlapped
    RECORDING_TYPE_REFERENCE,           //  allows post-recording & overlapped
} ;

[
    object,
    uuid(afd1f242-7efd-45ee-ba4e-407a25c9a77a),
    pointer_default(unique)
]
interface IStreamBufferSink : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        LockProfile ()

        1.  Locks the profile;
        2.  No *new* input pin connections will be accepted;
        3.  Existing pins that are, or have ever been, connected can be
            reconnected if the media type is exactly the same as the first
            successful connection;
        4.  Can be called multiple times safely with NULL parameter, but only
            once with non-NULL parameter; returns E_UNEXPECTED if called more
            than once with non-NULL param, or after the hosting filter has run;
        5.  Must be called before the filter that implements this interface is
            ever run; when it is run, it locks implicitely and this method has
            no effect if called with NULL parameters, or fails if called with
            non-NULL parameter for the reasons listed above;
        6.  Errors with VFW_E_UNSUPPORTED_STREAM if there are no streams in the
            profile;

        Parameter Detail
        ----------------

        pszStreamBufferFilename

            Is a NULL-terminated filename string.  If the content written by
            this sink is to be shared cross-process, this parameter specifies a
            filename that will be opened by any reader(s) to read & render the
            content sent into the sink.

            Can be NULL (not specified)

            Must be a full-path filename; if no path is specified, the file is
            created in a "current" directory

            If the file already exists, the call fails

            Is opened with DELETE_ON_CLOSE flag, so is automatically deleted
            when the sink is unlocked, or when the hosting process terminates
    --*/
    HRESULT
    LockProfile (
        [in]    LPCWSTR pszStreamBufferFilename
        ) ;

    /*++
        ------------------------------------------------------------------------
        CreateRecorder ()

        1.  Returns a *new* recorder object's IUnknown;
        2.  Caller can call QueryInterface() on the returned pointer to get
            interface pointers to configure & control the recording;
        3.  Returned IUnknown pointer is ref'd & must be Release()'d by the
            caller
        4.  IStreamBufferSink interface must have been locked (explicitely or
            implicitely) prior to call

        To create an ordinary recording, specify RECORDING_TYPE_CONTENT for the
        dwRecordType parammeter.  This will record the content directly into
        the specified file.  These recording types only accept start and stop
        times that occur in the future.

        A recording of type RECORDING_TYPE_REFERENCE generates a small file
        that references content saved in temporary storage.  Recordings of this
        type can have start and stop times that occurs in the past, and can
        overlap other same-type recordings.

        Reference recording *content* will be saved in the same subdirectory as
        the specified reference file, but with hidden and system attributes.
        The naming convention of the files will append a _1.sbe, _2.sbe, etc...
        to the filename (minus extension) specified in the call e.g. a
        "seinfeld01.sbe" reference file will have saved content in hidden
        and system files "seinfeld01_1.sbe", "seinfeld01_2.sbe", etc...

    --*/
    HRESULT
    CreateRecorder (
        [in]    LPCWSTR     pszFilename,
        [in]    DWORD       dwRecordType,       //  RECORDING_TYPE_CONTENT or RECORDING_TYPE_REFERENCE
        [out]   IUnknown ** pRecordingIUnknown
        ) ;

    /*++
        ------------------------------------------------------------------------
        IsProfileLocked ()

        1.  Returns S_OK if the profile is locked and S_FALSE if it is not.
        2.  Returns E_FAIL on error.
    --*/
    HRESULT
    IsProfileLocked (
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferSource ()

    Stream Source reader interface;
    Implemented on the StreamBufferSource filter;
--*/
[
    object,
    uuid(1c5bd776-6ced-4f44-8164-5eab0e98db12),
    pointer_default(unique)
]
interface IStreamBufferSource : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        SetStreamSink ()

        1.  Sets the StreamBuffer Sink that streams from this Source;
        2.  IStreamBufferSink object must be in the same process as this object;
        3.  Interface is AddRef()'d if the call succeeds;

        Parameter Detail
        ----------------

        pIStreamBufferSink

            Sink that will stream to this Source
    --*/
    HRESULT
    SetStreamSink (
        [in]    IStreamBufferSink *    pIStreamBufferSink
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferRecordControl

    obtained by QIing IStreamBufferSink::CreateRecorder()-returned IUnknown *
--*/
[
    object,
    uuid(ba9b6c99-f3c7-4ff2-92db-cfdd4851bf31),
    pointer_default(unique)
]
interface IStreamBufferRecordControl : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        Start ()

        1.  Starts a recording;
        2.  Will save to the filename that is specified when this interface's
            IUnknown is requested (IStreamBufferSink::CreateRecorder());

        Parameter Detail
        ----------------

        rtStart

            Start time relative to "now;

            If the recording type is a content recording, can only refer to
            seconds in the future; allowed seconds are [0,5]

            If the recording type is a reference recording, can refer to any
            time that still has valid content i.e. content that has not yet
            become stale

            If the recording is a reference recording and (* prtStart) is
            earlier than the earliest still-valid content, the call will reset
            it to the earliest content; the value when the recording was
            actually started will be [out]
    --*/
    HRESULT
    Start (
        [in,out]    REFERENCE_TIME *    prtStart
        ) ;

    /*++
        ------------------------------------------------------------------------
        Stop ()

        1.  Stops a recording;
        2.  Closes out the file;

        Parameter Detail
        ----------------

        rtStart

            Stop time relative to "now;

            If the recording type is a content recording, can only refer to
            seconds in the future; allowed seconds are [0,5]

            If the recording type is a reference recording, can refer to any
            time that still has valid content i.e. content that has not yet
            become stale; stop time cannot be <= start time
    --*/
    HRESULT
    Stop (
        [in]    REFERENCE_TIME  rtStop
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetRecordingStatus ()

        1.  Retrieves the status of the recording

        Parameter Detail
        ----------------

        phResult

            The (current) status of writing or closing the recording file;

            Can be NULL;

        pbStarted

            If supplied, set to a non-zero value if the recording has been
            started

            Can be NULL;

        pbStopped

            If supplied, set to a non-zero value if the recording has been
            stopped;

            Can be NULL;

        NOTE: If the recording has never been started, it will not be flagged
                as stopped.

    --*/
    HRESULT
    GetRecordingStatus (
        [out] HRESULT * phResult,
        [out] BOOL *    pbStarted,
        [out] BOOL *    pbStopped
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferRecComp

    CoCreateInstance CLSID_StreamBufferComposeRecording and QueryInterface for
    this interface; this interface allows the creation of a single target
    content recording which consists of a number of concatenated recordings
    (reference or content; can mix & match if desired)
--*/

[
    object,
    uuid(9E259A9B-8815-42ae-B09F-221970B154FD),
    pointer_default(unique)
]
interface IStreamBufferRecComp : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        Initialize ()

        1. Initializes for a target recording

        Parameter Detail
        ----------------

        pszTargetFilename

            Sets the target filename

            Fails if the file already exists

        pszSBRecProfileRef

            Must be a completed, SBE-generated recording

            This recording's profile will be used to define the target profile

            Appended files must have exactly the same profile
    --*/
    HRESULT
    Initialize (
        [in]    LPCWSTR pszTargetFilename,
        [in]    LPCWSTR pszSBRecProfileRef
        ) ;

    /*++
        ------------------------------------------------------------------------
        Append ()

        1.  appends an entire recording
        2.  fails if the recording is live
    --*/
    HRESULT
    Append (
        [in]    LPCWSTR pszSBRecording
        ) ;

    /*++
        ------------------------------------------------------------------------
        AppendEx ()

        1.  appends the specified portion of the recording; the parameters must
            be accurate; the call will not readjust them within the boundaries
        2.  the time spread must be at least 2 seconds
        3.  fails if the recording is live
    --*/
    HRESULT
    AppendEx (
        [in]    LPCWSTR         pszSBRecording,
        [in]    REFERENCE_TIME  rtStart,
        [in]    REFERENCE_TIME  rtStop
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetCurrentLength ()

        1.  returns the current length of the recording; updates as recordings
            are appended;
        2.  can be called repeatedly during a Append() call on another
            thread;
    --*/
    HRESULT
    GetCurrentLength (
        [out]   DWORD * pcSeconds
        ) ;

    /*++
        ------------------------------------------------------------------------
        Close ()

        1.  explicitely closes the recording

        2.  final release of interface closes the recording as well
    --*/
    HRESULT
    Close (
        ) ;

    /*++
        ------------------------------------------------------------------------
        Cancel ()

        1.  cancels an in-progress appending operation; has no effect otherwise
    --*/
    HRESULT
    Cancel (
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferRecordingAttribute

    obtained by calling QueryInterface on a recorder

    well-known attributes:

        NAME                DESCRIPTION
        ------------------- ----------------------------------------------------

        Title               String containing the content title.

        Author              String containing the name of the content author.

        Description         String containing a description of the content.

        Rating              String containing a content rating.

        Copyright           String containing a content copyright message.

        Duration            Quadruple word value containing the playing duration
                                of the file, in 100-nanosecond units.

        Bitrate             Double word value containing the bit rate.

        Seekable            Boolean value; true denoting that the content is
                                seekable.

        Stridable           Boolean value, true denoting that the content is
                                stridable (fast forward and rewind are enabled).

        Broadcast           Boolean value; true denoting that the content is not
                                copyright-protected, and can be broadcast.

        Use_DRM             reserved

        DRM_Flags           reserved

        DRM_Level           reserved

        Is_Protected        reserved

        Is_Trusted          reserved

        Signature_Name      reserved

        HasAudio            Boolean, true denoting the content includes an
                                audio stream.

        HasImage            Boolean, true denoting the content includes a still
                                image stream (such as JPEG images).

        HasScript           Boolean, true denoting the content includes a script
                                stream.

        HasVideo            Boolean, true denoting the content includes a video
                                stream.

        CurrentBitrate      Double word containing the current total bitrate,
                                usually used for MEB (multi-bit rate) streams.

        OptimalBitrate      Double word containing the minimum total bitrate
                                recommended to stream the content and get
                                maximum quality.

        WM/AlbumTitle       String containing the album title.

        WM/Track            Double word containing the track number.

        WM/PromotionURL     String with a URL to an HTML page that contains
                                information about products and events (such as
                                concerts) that are related to this music.

        WM/AlbumCoverURL    String with a URL to an HTML page that contains an
                                image of the album cover and information about
                                the album.

        WM/Genre            String with the genre of the music.

        WM/Year             String with the year of publication of the music.

        WM/GenreID

        WM/MCDI

        BannerImageType     One member of the WMT_ATTR_IMAGETYPE enumeration
                                type.

        BannerImageData     The actual image data: a bitmap, JPEG, or GIF image.


        BannerImageURL      If the banner image is clicked on then this URL is
                                activated.

        CopyrightURL        An URL to a copyright page.

        NSC_Name            String containing the multicast station contact
                                name (read-only).

        NSC_Address         String containing the multicast station contact
                                address (read-only).

        NSC_Phone           String containing the multicast station contact
                                phone number (read-only).

        NSC_Email           String containing the multicast station contact
                                email address (read-only).

        NSC_Description     String containing the multicast station contact
                                description (read-only).

--*/

cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// List of pre-defined attributes " )
cpp_quote( "//" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingDuration[] =L\"Duration\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingBitrate[] =L\"Bitrate\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingSeekable[] =L\"Seekable\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingStridable[] =L\"Stridable\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingBroadcast[] =L\"Broadcast\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingProtected[] =L\"Is_Protected\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingTrusted[] =L\"Is_Trusted\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingSignature_Name[] =L\"Signature_Name\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasAudio[] =L\"HasAudio\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasImage[] =L\"HasImage\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasScript[] =L\"HasScript\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasVideo[] =L\"HasVideo\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingCurrentBitrate[] =L\"CurrentBitrate\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingOptimalBitrate[] =L\"OptimalBitrate\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasAttachedImages[] =L\"HasAttachedImages\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingSkipBackward[] =L\"Can_Skip_Backward\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingSkipForward[] =L\"Can_Skip_Forward\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingNumberOfFrames[] =L\"NumberOfFrames\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingFileSize[] =L\"FileSize\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasArbitraryDataStream[] =L\"HasArbitraryDataStream\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingHasFileTransferStream[] =L\"HasFileTransferStream\";" )
cpp_quote( "" )
cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// The content description object supports 5 basic attributes." )
cpp_quote( "//" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingTitle[] =L\"Title\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingAuthor[] =L\"Author\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingDescription[] =L\"Description\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingRating[] =L\"Rating\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingCopyright[] =L\"Copyright\";" )
cpp_quote( "" )
cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// These attributes are used to configure DRM using IWMDRMWriter::SetDRMAttribute." )
cpp_quote( "//" )
cpp_quote( "static const WCHAR *g_wszStreamBufferRecordingUse_DRM = L\"Use_DRM\";" )
cpp_quote( "static const WCHAR *g_wszStreamBufferRecordingDRM_Flags = L\"DRM_Flags\";" )
cpp_quote( "static const WCHAR *g_wszStreamBufferRecordingDRM_Level = L\"DRM_Level\";" )
cpp_quote( "" )
cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// These are the additional attributes defined in the WM attribute" )
cpp_quote( "// namespace that give information about the content." )
cpp_quote( "//" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingAlbumTitle[] =L\"WM/AlbumTitle\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingTrack[] =L\"WM/Track\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingPromotionURL[] =L\"WM/PromotionURL\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingAlbumCoverURL[] =L\"WM/AlbumCoverURL\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingGenre[] =L\"WM/Genre\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingYear[] =L\"WM/Year\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingGenreID[] =L\"WM/GenreID\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingMCDI[] =L\"WM/MCDI\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingComposer[] =L\"WM/Composer\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingLyrics[] =L\"WM/Lyrics\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingTrackNumber[] =L\"WM/TrackNumber\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingToolName[] =L\"WM/ToolName\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingToolVersion[] =L\"WM/ToolVersion\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingIsVBR[] =L\"IsVBR\";" )

//
// WM/AlbumArtist is a potentially different value than Author
//
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingAlbumArtist[] =L\"WM/AlbumArtist\";" )
cpp_quote( "" )

cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// These optional attributes may be used to give information " )
cpp_quote( "// about the branding of the content." )
cpp_quote( "//" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingBannerImageType[] =L\"BannerImageType\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingBannerImageData[] =L\"BannerImageData\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingBannerImageURL[] =L\"BannerImageURL\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingCopyrightURL[] =L\"CopyrightURL\";" )

cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// Optional attributes, used to give information " )
cpp_quote( "// about video stream properties." )
cpp_quote( "//" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingAspectRatioX[] =L\"AspectRatioX\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingAspectRatioY[] =L\"AspectRatioY\";" )

cpp_quote( "////////////////////////////////////////////////////////////////" )
cpp_quote( "//" )
cpp_quote( "// The NSC file supports the following attributes." )
cpp_quote( "//" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingNSCName[] =L\"NSC_Name\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingNSCAddress[] =L\"NSC_Address\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingNSCPhone[] =L\"NSC_Phone\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingNSCEmail[] =L\"NSC_Email\";" )
cpp_quote( "static const WCHAR g_wszStreamBufferRecordingNSCDescription[] =L\"NSC_Description\";" )
cpp_quote( "" )

//
// StreamBuffer Attribute datatypes;
//
typedef enum {
    STREAMBUFFER_TYPE_DWORD  = 0,
    STREAMBUFFER_TYPE_STRING = 1,
    STREAMBUFFER_TYPE_BINARY = 2,
    STREAMBUFFER_TYPE_BOOL   = 3,
    STREAMBUFFER_TYPE_QWORD  = 4,
    STREAMBUFFER_TYPE_WORD   = 5,
    STREAMBUFFER_TYPE_GUID   = 6,
} STREAMBUFFER_ATTR_DATATYPE ;

[
    object,
    uuid(16CA4E03-FE69-4705-BD41-5B7DFC0C95F3),
    pointer_default(unique)
]
interface IStreamBufferRecordingAttribute : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        SetAttribute ()

        1.  Sets an attribute on a recording object;
        2.  Fails if the IStreamBufferRecordControl::Start has already been successfully
            called;
        3.  If an attribute of the same name already exists, overwrites the old;
    --*/
    HRESULT
    SetAttribute (
        [in]    ULONG                       ulReserved,
        [in]    LPCWSTR                     pszAttributeName,
        [in]    STREAMBUFFER_ATTR_DATATYPE  StreamBufferAttributeType,
        [in]    BYTE *                      pbAttribute,
        [in]    WORD                        cbAttributeLength
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetAttributeCount ()

        1.  Returns the count of attributes currently set;
    --*/
    HRESULT
    GetAttributeCount (
        [in]    ULONG   ulReserved,
        [out]   WORD *  pcAttributes
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetAttributeByName ()

        1.  Given a name, returns the attribute data;
        2.  If the provided buffer is too small, returns VFW_E_BUFFER_OVERFLOW,
            and (* pcbLength) contains the minimum required length of the buffer
        3.  To learn the length of the attribute, pass in non-NULL pcbLength,
            and NULL pbAttribute parameter; [out] value will be the length of
            the attribute
    --*/
    HRESULT
    GetAttributeByName (
        [in]        LPCWSTR                         pszAttributeName,
        [in]        ULONG *                         pulReserved,
        [out]       STREAMBUFFER_ATTR_DATATYPE *    pStreamBufferAttributeType,
        [out]       BYTE *                          pbAttribute,
        [in, out]   WORD *                          pcbLength
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetAttributeByIndex ()

        1.  Given an 0-based index, returns the attribute name and data
        2.  If either buffer is too small, returns VFW_E_BUFFER_OVERFLOW, and
            (* pcbLength) and (* pcchNameLength) contain the minimum required
            length of each buffer
        3.  The length returned by pcchNameLength includes the null-terminator
        4.  To learn the length of the name & attribute, pass in non-NULL
            pcchNameLength & pcbLength, and NULL pszAttributeName & pbAttribute
            parameters; [out] value of the non-NULL parameters will be the
            lengths of the name and attribute
    --*/
    HRESULT
    GetAttributeByIndex (
        [in]        WORD                            wIndex,
        [in]        ULONG *                         pulReserved,
        [out]       WCHAR *                         pszAttributeName,
        [in, out]   WORD *                          pcchNameLength,         //  includes NULL-terminator; in BYTES
        [out]       STREAMBUFFER_ATTR_DATATYPE *    pStreamBufferAttributeType,
        [out]       BYTE *                          pbAttribute,
        [in, out]   WORD *                          pcbLength
        ) ;

    /*++
        ------------------------------------------------------------------------
        EnumAttributes ()

        1.  Returns a StreamBuffer attribute enumeration object that snapshots
            the attributes at time-of-call
    --*/
    HRESULT
    EnumAttributes (
        [out]   IEnumStreamBufferRecordingAttrib **     ppIEnumStreamBufferAttrib
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IEnumStreamBufferRecordingAttrib

    obtained by calling IStreamBufferRecordingAttribute::EnumAttributes, or
    calling clone on this interface
--*/

typedef struct {
    LPWSTR                      pszName ;                   //  allocated by callee; freed by caller
    STREAMBUFFER_ATTR_DATATYPE  StreamBufferAttributeType ;
    BYTE *                      pbAttribute ;               //  allocated by callee; freed by caller
    WORD                        cbLength ;
} STREAMBUFFER_ATTRIBUTE ;

[
    object,
    uuid (C18A9162-1E82-4142-8C73-5690FA62FE33),
    pointer_default(unique)
]
interface IEnumStreamBufferRecordingAttrib : IUnknown
{
    HRESULT
    Next (
        [in]                            ULONG                       cRequest,
        [in, out, size_is (cRequest)]   STREAMBUFFER_ATTRIBUTE *    pStreamBufferAttribute,
        [out]                           ULONG *                     pcReceived
        ) ;

    HRESULT
    Skip (
        [in]    ULONG   cRecords
        ) ;

    HRESULT
    Reset (
        ) ;

    HRESULT
    Clone (
        [out]   IEnumStreamBufferRecordingAttrib **  ppIEnumStreamBufferAttrib
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferConfigure

--*/
[
    object,
    uuid(ce14dfae-4098-4af7-bbf7-d6511f835414),
    pointer_default(unique)
]
interface IStreamBufferConfigure : IUnknown
{
    /*++
        ------------------------------------------------------------------------
        SetStreamBufferDirectory ()

        1.  Sets the directory where all content is saved, ringbuffer &
            StreamBuffer;
        2.  Creates directory if necessary;
        3.  All TEMP files have hidden+system attributes
    --*/
    HRESULT
    SetDirectory (
        [in]    LPCWSTR pszDirectoryName
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetStreamBufferDirectory ()

        1.  Retrieves previously set backing store directory, or default
            location if none was specified
    --*/
    HRESULT
    GetDirectory (
        [out]   LPWSTR *    ppszDirectoryName
        ) ;

    /*++
        ------------------------------------------------------------------------
        SetBackingFileCount ()

        1.  Sets the number of backing files
        2.  valid values

                4 <= min <= 100
                6 <= max <= 102
                min max delta >= 2
    --*/
    HRESULT
    SetBackingFileCount (
        [in]    DWORD   dwMin,
        [in]    DWORD   dwMax
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetBackingFileCount ()

        1.  Retrieves previously set backing file counts, or defaults if none
            have have been set
    --*/
    HRESULT
    GetBackingFileCount (
        [out]   DWORD * pdwMin,
        [out]   DWORD * pdwMax
        ) ;

    /*++
        ------------------------------------------------------------------------
        SetEachBackingFileDuration ()

        1.  Sets the seconds of content each backing file will hold
        2.  valid values:

                dwSeconds >= 15
    --*/
    HRESULT
    SetBackingFileDuration (
        [in]    DWORD   dwSeconds
        ) ;

    /*++
        ------------------------------------------------------------------------
        GetEachBackingFileDuration ()

        1.  Retrieves previously set backing file duration, or default of none
            is set
    --*/
    HRESULT
    GetBackingFileDuration (
        [out]   DWORD * pdwSeconds
        ) ;
} ;

/*++
    ============================================================================
    ============================================================================
    IStreamBufferMediaSeeking

    Implemented on the StreamBufferSource filter.  Used to seek and set the
    playback rate.

--*/

[
    object,
    uuid(f61f5c26-863d-4afa-b0ba-2f81dc978596),
    pointer_default(unique)
]
interface IStreamBufferMediaSeeking : IMediaSeeking
{
    //  no additional methods have been added
} ;

/*++
    ============================================================================
    ============================================================================
    events
--*/

//  see evcode.h comment for range
//  stream buffer engine (PVR)   0x0326 - 0x0350 (sbe.idl)

cpp_quote ("#define STREAMBUFFER_EC_BASE                     0x0326")

cpp_quote ("enum {")
cpp_quote ("    //  timehole event")
cpp_quote ("    //      param1 = timehole stream offset ms")
cpp_quote ("    //      param1 = timehole size ms")
cpp_quote ("    STREAMBUFFER_EC_TIMEHOLE = STREAMBUFFER_EC_BASE,")
cpp_quote ("    ")
cpp_quote ("    STREAMBUFFER_EC_STALE_DATA_READ,")
cpp_quote ("    ")
cpp_quote ("    STREAMBUFFER_EC_STALE_FILE_DELETED,")
cpp_quote ("    STREAMBUFFER_EC_CONTENT_BECOMING_STALE,")
cpp_quote ("    STREAMBUFFER_EC_WRITE_FAILURE,")
cpp_quote ("    //")
cpp_quote ("    //  unexpected read failure")
cpp_quote ("    //      param1 = HRESULT failure")
cpp_quote ("    //      param2 = undefined")
cpp_quote ("    STREAMBUFFER_EC_READ_FAILURE,")
cpp_quote ("    //")
cpp_quote ("    //  playback rate change")
cpp_quote ("    //      param1 = old_playback_rate * 10000 e.g. 2x is 20000")
cpp_quote ("    //      param2 = new_playback_rate * 10000")
cpp_quote ("    STREAMBUFFER_EC_RATE_CHANGED,")
cpp_quote ("} ;")

