// GuideStore.idl : IDL source for GuideStore.dll
//

// This file will be processed by the MIDL tool to
// produce the type library (GuideStore.tlb) and marshalling code.

import "oaidl.idl";
import "ocidl.idl";

interface IObjects;
interface IMetaProperties;
interface IMetaProperty;
interface IMetaPropertyCondition;
interface IMetaPropertySet;
interface IMetaPropertySets;
interface IMetaPropertyType;
interface IMetaPropertyTypes;

interface IGuideStore;
interface IService;
interface IServices;
interface IProgram;
interface IPrograms;
interface IScheduleEntry;
interface IScheduleEntries;
interface IChannel;
interface IChannels;
interface IChannelLineup;
interface IChannelLineups;
interface IGuideDataProvider;
interface IGuideDataProviders;

#define didAdd						 10
#define didAddAt					 15
#define didAddNew					 20
#define didAddNewAt					 25
#define didAnd						 30
#define didBeginTrans				 35
#define didChannelLineups			 40
#define didChannels					 50
#define didCommitTrans				 55
#define didCond						 60
#define didCopyrightDate			 70
#define didCount					 80
#define didDataEndTime				 90
#define didDefaultValue				100
#define didDescription				110
#define didEndTime					120
#define didGuideDataProvider		135
#define didGuideDataProviders		136
#define didID						150
#define didIdOf						155
#define didIID						170
#define didIsAnyDataAvailable		180
#define didInit						190
#if 0
#define didItem						DISPID_VALUE
#else
#define didItem						191
#endif
#define didItemAdded				195
#define didItemChanged				196
#define didItemRemoved				197
#define didItemsByKey				198
#define didItemsChanged				199
#define didItemsInTimeRange			200
#define didItemsWithCond			210
#define didItemWithKey				215
#define didItemsWithMetaProp			220
#define didItemsWithMetaPropType		230
#define didItemsWithService			240
#define didItemsWithType			400
#define didItemWithID				250
#define didItemWithIID				260
#define didItemWithName				270
#define didItemWithMetaPropTypeLang		280
#define didItemWithTypeProviderLang		285
#define didItemWithProviderName		290
#define didItemWithServiceAtTime	300
#define didLanguage					310
#define didLength					320
#define didLookup					325
#define didMax						330
#define didMin						340
#define didName						350
#define didNew						360
#define didNewCollection			370
#define didNot						380
#define didObjects					390
#define didObjectTypes				410
#define didOpen						420
#define didOr						430
#define didProgram					440
#define didPrograms					450
#define didRollbackTrans			455
#define didMetaProperties				460
#define didMetaPropertiesOf				465
#define didMetaPropertySet				470
#define didMetaPropertySets				480
#define didMetaPropertyType				490
#define didMetaPropertyTypes			500
#define didProviderDescription		510
#define didProviderName				520
#define didProviderNetworkName		530
#define didResync					535
#define didItemInvRelBy				540
#define didItemsInvRelBy			545
#define didItemsInvRelToBy			546
#define didItemRelBy				550
#define didItemsRelBy				555
#define didItemsRelToBy				556
#define didRemove					560
#define didRemoveAll				565
#define didSchedEntries				570
#define didService					580
#define didServices					590
#define didStartTime				600
#define didTitle					620
#define didType						630
#define didTuneRequest				640
#define didUnreferencedItems		650
#if 0
#define didValue					DISPID_VALUE
#else
#define didValue					660
#endif


[
	uuid(8D9EEDCE-21E9-4845-82A8-99CEC53E6DB2),
	version(1.0),
	helpstring("Microsoft TV GuideStore 1.0 Type Library")
]
library MSTVGS
{
	importlib("stdole2.tlb");
	[
		object,
		uuid(F71020D2-A467-4EB7-839A-63C8F40C7AB4),
		dual,
		helpstring("IMetaPropertySet Interface"),
		pointer_default(unique)
	]
	interface IMetaPropertySet : IDispatch
	{
		[propget, id(didName), helpstring("property Name")] HRESULT Name([out, retval] BSTR *pVal);
		[propget, id(didMetaPropertyTypes), helpstring("property MetaPropertyTypes")] HRESULT MetaPropertyTypes([out, retval] IMetaPropertyTypes* *pVal);
	};
	[
		object,
		uuid(E8FD768C-EC4E-4DAB-A09C-011E8ECAE4D2),
		dual,
		helpstring("IMetaPropertySets Interface"),
		pointer_default(unique),
		nonextensible
	]
	interface IMetaPropertySets : IDispatch
	{
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT index, [out, retval] IMetaPropertySet* *ppropset);
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItemWithName), helpstring("property ItemWithName")] HRESULT ItemWithName(BSTR bstrName, [out, retval] IMetaPropertySet* *ppropset);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew(BSTR bstrName, [out, retval] IMetaPropertySet **pppropset);
		[propget, id(didLookup), helpstring("property Lookup")] HRESULT Lookup([in] BSTR bstr, [out, retval] IMetaPropertyType * *ppproptype);
	};
	[
		object,
		uuid(86502400-213B-4ADF-A1E2-76365E7172BD),
		dual,
		helpstring("IMetaPropertyType Interface"),
		pointer_default(unique)
	]
	interface IMetaPropertyType : IDispatch
	{
		[propget, id(didMetaPropertySet), helpstring("property MetaPropertySet")] HRESULT MetaPropertySet([out, retval] IMetaPropertySet* *ppropset);
		[propget, id(didID), helpstring("property ID")] HRESULT ID([out, retval] long *pVal);
		[propget, id(didName), helpstring("property Name")] HRESULT Name([out, retval] BSTR *pVal);
		[propget, id(didNew), helpstring("property New")] HRESULT New(long lang, VARIANT val, [out, retval] IMetaProperty* *pprop);
		[propget, id(didCond), helpstring("property Cond")] HRESULT Cond(BSTR bstrCond, long lang, VARIANT varValue, [out, retval] IMetaPropertyCondition* *ppropcond);
	};
	[
		object,
		uuid(9BF4984A-4CFE-4588-9FCF-828C74EF7104),
		dual,
		helpstring("IMetaPropertyTypes Interface"),
		pointer_default(unique)
	]
	interface IMetaPropertyTypes : IDispatch
	{
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT index, [out, retval] IMetaPropertyType* *pproptype);
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItemWithID), helpstring("property ItemWithID")] HRESULT ItemWithID(long id, [out, retval] IMetaPropertyType* *pproptype);
		[propget, id(didItemWithName), helpstring("property ItemWithName")] HRESULT ItemWithName(BSTR bstrName, [out, retval] IMetaPropertyType* *pproptype);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew(long id, BSTR bstrName, [out, retval] IMetaPropertyType * *pVal);
		[propget, id(didMetaPropertySet), helpstring("property MetaPropertySet")] HRESULT MetaPropertySet([out, retval] IMetaPropertySet * *pVal);
	};
	[
		object,
		uuid(A4BBD2C0-D7E4-4FC2-8FB0-176DDBCB3D72),
		dual,
		helpstring("IMetaProperty Interface"),
		pointer_default(unique)
	]
	interface IMetaProperty : IDispatch
	{
		[propget, id(didMetaPropertyType), helpstring("property MetaPropertyType")] HRESULT MetaPropertyType([out, retval] IMetaPropertyType* *pproptype);
		[propget, id(didLanguage), helpstring("property Language")] HRESULT Language([out, retval] long *pVal);
		[propget, id(didGuideDataProvider), helpstring("property GuideDataProvider")] HRESULT GuideDataProvider([out, retval] IGuideDataProvider **ppprovider);
		[propget, id(didValue), helpstring("property Value")] HRESULT Value([out, retval] VARIANT *pvarValue);
		[propput, id(didValue), helpstring("property Value")] HRESULT Value([in] VARIANT varValue);
		[propputref, id(didValue), helpstring("property Value")] HRESULT Value([in] IUnknown *punk);
		[propget, id(didCond), helpstring("property Cond")] HRESULT Cond(BSTR bstrCond, [out, retval] IMetaPropertyCondition* *ppropcond);
	};
	[
		object,
		uuid(E7F78F69-8326-48A0-8E54-BBDCEE43BA70),
		dual,
		helpstring("IMetaProperties Interface"),
		pointer_default(unique)
	]
	interface IMetaProperties : IDispatch
	{
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT index, [out, retval] IMetaProperty* *pprop);
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItemWithMetaPropTypeLang), helpstring("property ItemWith")] HRESULT ItemWith(IMetaPropertyType *ptype, long lang, [out, retval] IMetaProperty* *pprop);
		[propget, id(didItemWithTypeProviderLang), helpstring("property ItemWithTypeProviderLang")] HRESULT ItemWithTypeProviderLang(IMetaPropertyType *ptype, IGuideDataProvider *pprovider, long lang, [out, retval] IMetaProperty* *pprop);
		[propget, id(didItemsWithMetaPropType), helpstring("property ItemsWithMetaPropertyType")] HRESULT ItemsWithMetaPropertyType(IMetaPropertyType *ptype, [out, retval] IMetaProperties* *pprops);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew(IMetaPropertyType *pproptype, long lang, VARIANT varValue, [out, retval] IMetaProperty * *pVal);
		[id(didAdd), helpstring("method Add")] HRESULT Add(IMetaProperty *pprop);
	};
	[
		object,
		uuid(98FAAEF5-397A-4372-93A3-FB3DA49B3EF1),
		dual,
		helpstring("IMetaPropertyCondition Interface"),
		pointer_default(unique)
	]
	interface IMetaPropertyCondition : IDispatch
	{
		[propget, id(didAnd), helpstring("property And")] HRESULT And(IMetaPropertyCondition *pcond2, [out, retval] IMetaPropertyCondition* *ppropcond);
		[propget, id(didOr), helpstring("property Or")] HRESULT Or(IMetaPropertyCondition *pcond2, [out, retval] IMetaPropertyCondition* *ppropcond);
	};
	[
		object,
		uuid(E4A9F7DA-F38F-43D3-AB3B-7E9F9FB7A7C7),
		dual,
		helpstring("IGuideStore Interface"),
		pointer_default(unique)
	]
	interface IGuideStore : IDispatch
	{
		[propget, id(didIID), helpstring("property UUID")] HRESULT UUID([out, retval] BSTR  *bstrUUID);
		[propget, id(didServices), helpstring("property Services")] HRESULT Services([out, retval] IServices * *pVal);
		[propget, id(didPrograms), helpstring("property Programs")] HRESULT Programs([out, retval] IPrograms * *pVal);
		[propget, id(didSchedEntries), helpstring("property ScheduleEntries")] HRESULT ScheduleEntries([out, retval] IScheduleEntries * *pVal);
		[propget, id(didGuideDataProviders), helpstring("property GuideDataProviders")] HRESULT GuideDataProviders([out, retval] IGuideDataProviders * *ppdataproviders);
		[propget, id(didMetaPropertySets), helpstring("property MetaPropertySets")] HRESULT MetaPropertySets([out, retval] IMetaPropertySets **pppropsets);
		[id(didOpen), helpstring("method Open")] HRESULT Open(BSTR bstrName);
		[propget, id(didObjects), helpstring("property Objects")] HRESULT Objects([out, retval] IObjects * *ppobjs);
		[propget, id(didChannels), helpstring("property Channels")] HRESULT Channels([out, retval] IChannels * *pVal);
		[propget, id(didChannelLineups), helpstring("property ChannelLineups")] HRESULT ChannelLineups([out, retval] IChannelLineups * *pVal);
		[propget, id(didGuideDataProvider), helpstring("property ActiveGuideDataProvider")] HRESULT ActiveGuideDataProvider([out, retval] IGuideDataProvider * *pVal);
		[propputref, id(didGuideDataProvider), helpstring("property ActiveGuideDataProvider")] HRESULT ActiveGuideDataProvider([in] IGuideDataProvider * newVal);


		[propget, id(didIdOf), helpstring("property IdOf")] HRESULT IdOf([in] IUnknown *punk, [out, retval] long *pVal);
		[propget, id(didMetaPropertiesOf), helpstring("property MetaPropertiesOf")] HRESULT MetaPropertiesOf([in] IUnknown *punk, [out, retval] IMetaProperties **ppprops);

		[id(didBeginTrans), helpstring("method BeginTrans")] HRESULT BeginTrans();
		[id(didCommitTrans), helpstring("method CommitTrans")] HRESULT CommitTrans();
		[id(didRollbackTrans), helpstring("method RollbackTrans")] HRESULT RollbackTrans();
	};

	[
		uuid(E7267FA2-7EC0-4577-BE37-0BBF11028A56),
		helpstring("MetaPropertySet Class")
	]
	coclass MetaPropertySet
	{
		[default] interface IMetaPropertySet;
	};
	[
		uuid(027D8BB9-B860-4B96-B498-7EA609F33250),
		helpstring("MetaPropertySets Class")
	]
	coclass MetaPropertySets
	{
		[default] interface IMetaPropertySets;
	};
	[
		uuid(A09139F3-47ED-4492-A45E-F7F11B121F4F),
		helpstring("MetaPropertyType Class")
	]
	coclass MetaPropertyType
	{
		[default] interface IMetaPropertyType;
	};
	[
		uuid(5F24A17F-1DDE-4F37-8B29-489229175C73),
		helpstring("MetaPropertyTypes Class")
	]
	coclass MetaPropertyTypes
	{
		[default] interface IMetaPropertyTypes;
	};
	[
		uuid(A42A1FF3-BC43-4714-8B94-06103474372B),
		helpstring("MetaProperty Class")
	]
	coclass MetaProperty
	{
		[default] interface IMetaProperty;
	};
	[
		uuid(78B8FA05-01B2-4B0A-B6E0-59FC6C0E7A5E),
		helpstring("MetaProperties Class")
	]
	coclass MetaProperties
	{
		[default] interface IMetaProperties;
	};
	[
		uuid(3B575572-EC9F-447D-9554-17C6E92E8328),
		helpstring("MetaPropertyCondition Class")
	]
	coclass MetaPropertyCondition
	{
		[default] interface IMetaPropertyCondition;
	};
	[
		object,
		uuid(59745450-F0F4-4B3F-B49E-55664E425CF6),
		dual,
		helpstring("IService Interface"),
		pointer_default(unique)
	]
	interface IService : IDispatch
	{
		[propget, id(didTuneRequest), helpstring("property TuneRequest")] HRESULT TuneRequest([out, retval] IUnknown * *ppunk);
		[propputref, id(didTuneRequest), helpstring("property TuneRequest")] HRESULT TuneRequest([in] IUnknown  *punk);
		[propget, id(didID), helpstring("property ID")] HRESULT ID([out, retval] long *pVal);
		[propget, id(didStartTime), helpstring("property StartTime")] HRESULT StartTime([out, retval] DATE *pdt);
		[propput, id(didStartTime), helpstring("property StartTime")] HRESULT StartTime([in] DATE dt);
		[propget, id(didEndTime), helpstring("property EndTime")] HRESULT EndTime([out, retval] DATE *pdt);
		[propput, id(didEndTime), helpstring("property EndTime")] HRESULT EndTime([in] DATE dt);
		[propget, id(didProviderName), helpstring("property ProviderName")] HRESULT ProviderName([out, retval] BSTR *pbstrName);
		[propput, id(didProviderName), helpstring("property ProviderName")] HRESULT ProviderName([in] BSTR bstrName);
		[propget, id(didProviderNetworkName), helpstring("property ProviderNetworkName")] HRESULT ProviderNetworkName([out, retval] BSTR *pbstrName);
		[propput, id(didProviderNetworkName), helpstring("property ProviderNetworkName")] HRESULT ProviderNetworkName([in] BSTR bstrName);
		[propget, id(didProviderDescription), helpstring("property ProviderDescription")] HRESULT ProviderDescription([out, retval] BSTR *pbstrDesc);
		[propput, id(didProviderDescription), helpstring("property ProviderDescription")] HRESULT ProviderDescription([in] BSTR bstrDescr);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
		[propget, id(didSchedEntries), helpstring("property ScheduleEntries")] HRESULT ScheduleEntries([out, retval] IScheduleEntries * *pVal);
	};
	[
		object,
		uuid(D3517044-B747-42C0-AFD5-31265ABA4977),
		dual,
		helpstring("IServices Interface"),
		pointer_default(unique)
	]
	interface IServices : IDispatch
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IService * *pVal);
		[propget, id(didChannelLineups), helpstring("property ChannelLineups")] HRESULT ChannelLineups([out, retval] IChannelLineups * *pVal);
		[id(didRemove), helpstring("method Remove")] HRESULT Remove(VARIANT varIndex);
		[propget, id(didItemWithID), helpstring("property ItemWithID")] HRESULT ItemWithID(long id, [out, retval] IService * *pVal);
		[propget, id(didItemWithProviderName), helpstring("property ItemWithProviderName")] HRESULT ItemWithProviderName(BSTR bstrProviderName, [out, retval] IService * *pVal);
		[propget, id(didItemsWithMetaProp), helpstring("property ItemsWithMetaProperty")] HRESULT ItemsWithMetaProperty(IMetaProperty *pprop, [out, retval] IServices * *ppservices);
		[propget, id(didItemsWithCond), helpstring("property ItemsWithMetaPropertyCond")] HRESULT ItemsWithMetaPropertyCond(IMetaPropertyCondition *pcond, [out, retval] IServices * *ppservices);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew(IUnknown *punkTuneRequest, BSTR bstrProviderName, BSTR bstrProviderDescription, BSTR bstrProviderNetworkName, DATE dtStart, DATE dtEnd, [out, retval] IService * *pVal);
		[propget, id(didItemsInTimeRange), helpstring("property ItemsInTimeRange")] HRESULT ItemsInTimeRange(DATE dtStart, DATE dtEnd, [out, retval] IServices * *pVal);
		[id(didRemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[id(didUnreferencedItems), helpstring("method UnreferencedItems")] HRESULT UnreferencedItems([out, retval] IServices **ppservices);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IServices * *ppservices);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IService * *ppservice);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};

	[
		uuid(C4001F96-2DEE-4C33-B807-F829889A8CCD),
		helpstring("GuideStore Class")
	]
	coclass GuideStore
	{
		[default] interface IGuideStore;
	};
	[
		uuid(957D8D57-32B1-4BE3-8E37-EC8849F16815),
		helpstring("Service Class")
	]
	coclass Service
	{
		[default] interface IService;
	};
	[
		uuid(CCFB9EC5-E28E-4DE1-BD07-1C79303DE0A0),
		helpstring("IServicesEvents Interface")
	]
	dispinterface IServicesEvents
	{
		properties:
		methods:
		[id(didItemAdded), helpstring("method ItemAdded")] HRESULT ItemAdded(IService *pservice);
		[id(didItemRemoved), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method ItemChanged")] HRESULT ItemChanged(IService *pservice);
		[id(didItemsChanged), helpstring("method ItemsChanged")] HRESULT ItemsChanged();
	};
	[
		object,
		uuid(FC91783E-5703-4319-A5B1-19555059559C),
		dual,
		helpstring("IProgram Interface"),
		pointer_default(unique)
	]
	interface IProgram : IDispatch
	{
		[propget, id(didID), helpstring("property ID")] HRESULT ID([out, retval] long *pVal);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
		[propget, id(didSchedEntries), helpstring("property ScheduleEntries")] HRESULT ScheduleEntries([out, retval] IScheduleEntries * *pVal);
		[propget, id(didTitle), helpstring("property Title")] HRESULT Title([out, retval] BSTR *pVal);
		[propput, id(didTitle), helpstring("property Title")] HRESULT Title([in] BSTR newVal);
		[propget, id(didDescription), helpstring("property Description")] HRESULT Description([out, retval] BSTR *pVal);
		[propput, id(didDescription), helpstring("property Description")] HRESULT Description([in] BSTR newVal);
		[propget, id(didCopyrightDate), helpstring("property CopyrightDate")] HRESULT CopyrightDate([out, retval] DATE *pVal);
		[propput, id(didCopyrightDate), helpstring("property CopyrightDate")] HRESULT CopyrightDate([in] DATE newVal);
	};
	[
		object,
		uuid(8786250A-8EF8-4A51-B80A-643CCF835DB6),
		dual,
		helpstring("IPrograms Interface"),
		pointer_default(unique)
	]
	interface IPrograms : IDispatch
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IProgram * *pVal);
		[propget, id(didItemWithID), helpstring("property ItemWithID")] HRESULT ItemWithID(long id, [out, retval] IProgram * *pVal);
		[propget, id(didItemsWithMetaProp), helpstring("property ItemsWithMetaProperty")] HRESULT ItemsWithMetaProperty(IMetaProperty *pprop, [out, retval] IPrograms * *pVal);
		[propget, id(didItemsWithCond), helpstring("property ItemsWithMetaPropertyCond")] HRESULT ItemsWithMetaPropertyCond(IMetaPropertyCondition *pcond, [out, retval] IPrograms * *pVal);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew([out, retval] IProgram * *ppprog);
		[id(didUnreferencedItems), helpstring("method UnreferencedItems")] HRESULT UnreferencedItems([out, retval] IPrograms **ppprogs);
		[id(didRemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[id(didRemove), helpstring("method Remove")] HRESULT Remove(VARIANT varIndex);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IPrograms * *ppprogs);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IProgram * *ppprog);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};

	[
		uuid(43F457D2-C955-48E2-91AD-B91C9154C613),
		helpstring("Services Class")
	]
	coclass Services
	{
		[default] interface IServices;
		[default, source] dispinterface IServicesEvents;
	};
	[
		uuid(C51F670A-7D1A-494E-931D-886BFDB2B438),
		helpstring("Program Class")
	]
	coclass Program
	{
		[default] interface IProgram;
	};
	[
		uuid(9AB9E463-1EC4-4D6B-AC80-5238561918EE),
		helpstring("IProgramsEvents Interface")
	]
	dispinterface IProgramsEvents
	{
		properties:
		methods:
		[id(didItemAdded), helpstring("method ItemAdded")] HRESULT ItemAdded(IProgram *pprog);
		[id(didItemRemoved), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method ItemChanged")] HRESULT ItemChanged(IProgram *pprog);
		[id(didItemsChanged), helpstring("method ItemsChanged")] HRESULT ItemsChanged();
	};
	[
		object,
		uuid(6C46F789-2156-4AF0-97D7-38D99E2C9160),
		dual,
		helpstring("IScheduleEntry Interface"),
		pointer_default(unique)
	]
	interface IScheduleEntry : IDispatch
	{
		[propget, id(didID), helpstring("property ID")] HRESULT ID([out, retval] long *pVal);
		[propget, id(didService), helpstring("property Service")] HRESULT Service([out, retval] IService * *ppservice);
		[propputref, id(didService), helpstring("property Service")] HRESULT Service([in] IService * pservice);
		[propget, id(didProgram), helpstring("property Program")] HRESULT Program([out, retval] IProgram * *ppprog);
		[propputref, id(didProgram), helpstring("property Program")] HRESULT Program([in] IProgram * pprog);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
		[propget, id(didStartTime), helpstring("property StartTime")] HRESULT StartTime([out, retval] DATE *pdt);
		[propput, id(didStartTime), helpstring("property StartTime")] HRESULT StartTime([in] DATE dt);
		[propget, id(didEndTime), helpstring("property EndTime")] HRESULT EndTime([out, retval] DATE *pdt);
		[propput, id(didEndTime), helpstring("property EndTime")] HRESULT EndTime([in] DATE dt);
		[propget, id(didLength), helpstring("property Length")] HRESULT Length([out, retval] long *pVal);
	};
	[
		object,
		uuid(E5FDD9C4-8E60-4BEB-BBC8-93BE39C75BAA),
		dual,
		helpstring("IScheduleEntries Interface"),
		pointer_default(unique)
	]
	interface IScheduleEntries : IDispatch
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IScheduleEntry * *pVal);
		[propget, id(didItemWithServiceAtTime), helpstring("property ItemWithServiceAtTime")] HRESULT ItemWithServiceAtTime(IService *pservice, DATE dt, [out, retval] IScheduleEntry * *pVal);
		[propget, id(didItemsWithService), helpstring("property ItemsWithService")] HRESULT ItemsWithService(IService *pservice, [out, retval] IScheduleEntries **ppschedentries);
		[propget, id(didItemsWithMetaProp), helpstring("property ItemsWithMetaProperty")] HRESULT ItemsWithMetaProperty(IMetaProperty *pprop, [out, retval] IScheduleEntries * *pVal);
		[propget, id(didItemsWithCond), helpstring("property ItemsWithMetaPropertyCond")] HRESULT ItemsWithMetaPropertyCond(IMetaPropertyCondition *pcond, [out, retval] IScheduleEntries * *pVal);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew(DATE dtStart, DATE dtEnd, IService *pservice, IProgram *pprog, [out, retval] IScheduleEntry * *pVal);
		[id(didRemove), helpstring("method Remove")] HRESULT Remove(VARIANT varIndex);
		[id(didRemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[propget, id(didItemsInTimeRange), helpstring("property ItemsInTimeRange")] HRESULT ItemsInTimeRange(DATE dtStart, DATE dtEnd, [out, retval] IScheduleEntries * *pVal);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IScheduleEntries * *ppschedentries);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IScheduleEntry * *ppschedentry);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};

	[
		uuid(16C9C579-B3F4-4C94-88EC-A65EA0B839E7),
		helpstring("Programs Class")
	]
	coclass Programs
	{
		[default] interface IPrograms;
		[default, source] dispinterface IProgramsEvents;
	};
	[
		uuid(AFEBCA90-0FF9-48BD-BC98-95477A631BBB),
		helpstring("ScheduleEntry Class")
	]
	coclass ScheduleEntry
	{
		[default] interface IScheduleEntry;
	};
	[
		uuid(32692A48-F4B5-4826-BE88-E7F8ED9E65DC),
		helpstring("IScheduleEntriesEvents Interface")
	]
	dispinterface IScheduleEntriesEvents
	{
		properties:
		methods:
		[id(didItemAdded), helpstring("method ItemAdded")] HRESULT ItemAdded(IScheduleEntry *pschedentry);
		[id(didItemRemoved), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method ItemChanged")] HRESULT ItemChanged(IScheduleEntry *pschedentry);
		[id(didItemsChanged), helpstring("method ItemsChanged")] HRESULT ItemsChanged();
	};
	[
		object,
		uuid(ED7DF8CD-**************-8EDE63A51F38),
		dual,
		helpstring("IChannel Interface"),
		pointer_default(unique)
	]
	interface IChannel : IDispatch
	{
		[propget, id(didName), helpstring("property Name")] HRESULT Name([out, retval] BSTR *pVal);
		[propput, id(didName), helpstring("property Name")] HRESULT Name([in] BSTR newVal);
		[propget, id(didService), helpstring("property Service")] HRESULT Service([out, retval] IService * *ppservice);
		[propputref, id(didService), helpstring("property Service")] HRESULT Service([in] IService *pservice);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
		[propget, id(didChannelLineups), helpstring("property ChannelLineups")] HRESULT ChannelLineups([out, retval] IChannelLineups * *pVal);
	};

	[
		uuid(226D6AD0-7026-494F-BCAD-FAB087E67290),
		helpstring("ScheduleEntries Class")
	]
	coclass ScheduleEntries
	{
		[default] interface IScheduleEntries;
		[default, source] dispinterface IScheduleEntriesEvents;
	};
	[
		object,
		uuid(3BAE53BD-70F0-4C7B-8C9E-E0317FFF8D79),
		dual,
		helpstring("IChannels Interface"),
		pointer_default(unique)
	]
	interface IChannels : IDispatch
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IChannel * *pVal);
		[id(didAddAt), helpstring("method AddAt")] HRESULT AddAt(IChannel *pchan, long index);
		[id(didRemove), helpstring("method Remove")] HRESULT Remove(VARIANT index);
		[propget, id(didAddNewAt), helpstring("property AddNewAt")] HRESULT AddNewAt(IService *pservice, BSTR bstrName, long index, [out, retval] IChannel * *pVal);
		[propget, id(didItemWithName), helpstring("property ItemWithName")] HRESULT ItemWithName(BSTR bstrName, [out, retval] IChannel **ppchan);
		[propget, id(didItemsWithCond), helpstring("property ItemsWithMetaPropertyCond")] HRESULT ItemsWithMetaPropertyCond(IMetaPropertyCondition *pcond, [out, retval] IChannels * *ppchannels);
		[id(didRemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[id(didUnreferencedItems), helpstring("method UnreferencedItems")] HRESULT UnreferencedItems([out, retval] IChannels **ppchans);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IChannels * *ppchans);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IChannel * *ppchan);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};

	[
		uuid(83568B75-1FCC-4853-957A-9CF617B350A3),
		helpstring("Channel Class")
	]
	coclass Channel
	{
		[default] interface IChannel;
	};
	[
		uuid(6E945C62-0AB7-4D89-BB9E-212502FC7C88),
		helpstring("IChannelsEvents Interface")
	]
	dispinterface IChannelsEvents
	{
		properties:
		methods:
		[id(didItemAdded), helpstring("method ItemAdded")] HRESULT ItemAdded(IChannel *pchan);
		[id(didItemRemoved), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method ItemChanged")] HRESULT ItemChanged(IChannel *pchan);
		[id(didItemsChanged), helpstring("method ItemsChanged")] HRESULT ItemsChanged();
	};
	[
		object,
		uuid(AB3FF8DB-C718-4ABD-98DE-E14DC74F4872),
		dual,
		helpstring("IChannelLineup Interface"),
		pointer_default(unique)
	]
	interface IChannelLineup : IDispatch
	{
		[propget, id(didName), helpstring("property Name")] HRESULT Name([out, retval] BSTR *pVal);
		[propput, id(didName), helpstring("property Name")] HRESULT Name([in] BSTR newVal);
		[propget, id(didChannels), helpstring("property Channels")] HRESULT Channels([out, retval] IChannels * *pVal);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
	};

	[
		uuid(73AF9077-4F6D-4FCB-A9E2-FDFBB9AE5310),
		helpstring("Channels Class")
	]
	coclass Channels
	{
		[default] interface IChannels;
		[default, source] dispinterface IChannelsEvents;
	};
	[
		object,
		uuid(2F78C3E1-98FE-4526-A0A7-A621025AEFF6),
		dual,
		helpstring("IChannelLineups Interface"),
		pointer_default(unique)
	]
	interface IChannelLineups : IDispatch
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IChannelLineup **ppchanlineup);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew(BSTR bstrName, [out, retval] IChannelLineup * *pVal);
		[id(didRemove), helpstring("method Remove")] HRESULT Remove(VARIANT varIndex);
		[id(didRemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[id(didUnreferencedItems), helpstring("method UnreferencedItems")] HRESULT UnreferencedItems([out, retval] IChannelLineups **ppchanlineups);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IChannelLineups * *ppchanlineups);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IChannelLineup * *ppchanlineup);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};

	[
		uuid(8F86A876-E12A-4159-9647-EAFE0288014F),
		helpstring("ChannelLineup Class")
	]
	coclass ChannelLineup
	{
		[default] interface IChannelLineup;
	};
	[
		uuid(1E3971E3-CCDC-445D-AE97-A15D5D4A40C9),
		helpstring("IChannelLineupsEvents Interface")
	]
	dispinterface IChannelLineupsEvents
	{
		properties:
		methods:
		[id(didItemAdded), helpstring("method ItemAdded")] HRESULT ItemAdded(IChannelLineup *pchanlineup);
		[id(didItemRemoved), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method ItemChanged")] HRESULT ItemChanged(IChannelLineup *pchanlineup);
		[id(didItemsChanged), helpstring("method ItemsChanged")] HRESULT ItemsChanged();
	};
	[
		object,
		uuid(A476A330-1123-4065-B3B7-D1EA899151BD),
		helpstring("IObject Interface"),
		pointer_default(unique)
	]
	interface IObject : IUnknown
	{
		[propget, id(didID), helpstring("property ID")] HRESULT ID([out, retval] long *pVal);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
		[propget, id(didItemRelBy), helpstring("property ItemRelatedBy")] HRESULT ItemRelatedBy([in] IMetaPropertyType *pproptype, [out, retval] IUnknown **ppobj);
		[propputref, id(didItemRelBy), helpstring("property ItemRelatedBy")] HRESULT ItemRelatedBy([in] IMetaPropertyType *pproptype, [in] IUnknown *pobj);
		[propget, id(didMetaPropertyType), helpstring("property MetaPropertyType")] HRESULT MetaPropertyType([in] BSTR bstr, [out, retval] IMetaPropertyType* *pproptype);
		[propget, id(didItemsWithType), helpstring("property ObjectsWithType")] HRESULT ObjectsWithType([in] BSTR bstrCLSID, [out, retval] IObjects **ppobjs);
	};
	[
		object,
		uuid(E8F1FBD5-4E44-4C26-B3D2-2C1C6999D611),
		helpstring("IObjects Interface"),
		pointer_default(unique)
	]
	interface IObjects : IUnknown
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *pVal);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IUnknown * *ppunk);
		[propget, id(didItemsWithType), helpstring("property ItemsWithType")] HRESULT ItemsWithType(BSTR bstrCLSID, [out, retval] IObjects * *pVal);
		[propget, id(didItemWithIID), helpstring("property ItemWithID")] HRESULT ItemWithID(long id, [out, retval] IUnknown * *ppunk);
		[propget, id(didItemsWithMetaProp), helpstring("property ItemsWithMetaProperty")] HRESULT ItemsWithMetaProperty(IMetaProperty *pprop, [out, retval] IObjects * *pVal);
		[propget, id(didItemsWithCond), helpstring("property ItemsWithMetaPropertyCond")] HRESULT ItemsWithMetaPropertyCond(IMetaPropertyCondition *ppropcond, [out, retval] IObjects * *pVal);
		[id(didAddAt), helpstring("method AddAt")] HRESULT AddAt(IUnknown *punk, long index);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew([out, retval] IUnknown * *ppunk);
		[id(didRemove), helpstring("method Remove")] HRESULT Remove(VARIANT varIndex);
		[id(didRemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[propget, id(didAddNewAt), helpstring("property AddNewAt")] HRESULT AddNewAt([in] long index, [out, retval] IUnknown * *ppunk);
		[propget, id(didItemsInTimeRange), helpstring("property ItemsInTimeRange")] HRESULT ItemsInTimeRange(DATE dtStart, DATE dtEnd, [out, retval] IObjects * *pVal);
		[propget, id(didItemsRelToBy), helpstring("property ItemsRelatedToBy")] HRESULT ItemsRelatedToBy([in] IUnknown *pobj, [in] IMetaPropertyType *pproptype, [out, retval] IObjects * *ppobjs);
		[propget, id(didItemsInvRelToBy), helpstring("property ItemsInverseRelatedToBy")] HRESULT ItemsInverseRelatedToBy([in] IUnknown *pobj, [in] IMetaPropertyType *pproptype, [out, retval] IObjects * *ppobjs);
		[id(didUnreferencedItems), helpstring("method UnreferencedItems")] HRESULT UnreferencedItems([out, retval] IObjects **ppobjs);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IObjects * *pVal);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IUnknown * *ppunk);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};

	[
		uuid(8520EF80-1C19-4CB0-83FA-67DB59CC9AE4),
		helpstring("ChannelLineups Class")
	]
	coclass ChannelLineups
	{
		[default] interface IChannelLineups;
		[default, source] dispinterface IChannelLineupsEvents;
	};
	[
		uuid(83375A19-A098-42CF-9206-EE36FE48C637),
		helpstring("IObjectsNotifications Interface")
	]
	interface IObjectsNotifications : IUnknown
	{
		[id(didItemAdded), helpstring("method Notify_ItemAdded")] HRESULT Notify_ItemAdded(IUnknown *punk);
		[id(didItemRemoved), helpstring("method Notify_ItemRemoved")] HRESULT Notify_ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method Notify_ItemChanged")] HRESULT Notify_ItemChanged(IUnknown *punk);
		[id(didItemsChanged), helpstring("method Notify_ItemsChanged")] HRESULT Notify_ItemsChanged();
	};

	[
		uuid(B485447D-B180-420F-B2A5-E7BBCEA07EAD),
		aggregatable,
		helpstring("Objects Class")
	]
	coclass Objects
	{
		[default] interface IObjects;
	};
	[
		object,
		uuid(4B16049B-E548-4868-B303-D501340E2CB1),
		dual,
		helpstring("IGuideDataProvider Interface"),
		pointer_default(unique)
	]
	interface IGuideDataProvider : IDispatch
	{
		[propget, id(didID), helpstring("property ID")] HRESULT ID([out, retval] long *pVal);
		[propget, id(didName), helpstring("property Name")] HRESULT Name([out, retval] BSTR *pbstrName);
		[propget, id(didDescription), helpstring("property Description")] HRESULT Description([out, retval] BSTR *pbstrDesc);
		[propput, id(didDescription), helpstring("property Description")] HRESULT Description([in] BSTR bstrDesc);
		[propget, id(didMetaProperties), helpstring("property MetaProperties")] HRESULT MetaProperties([out, retval] IMetaProperties * *pVal);
	};
	[
		object,
		uuid(CA9DE996-637C-47BF-BC10-CF956BE298EC),
		dual,
		helpstring("IGuideDataProviders Interface"),
		pointer_default(unique)
	]
	interface IGuideDataProviders : IDispatch
	{
		[propget, id(didCount), helpstring("property Count")] HRESULT Count([out, retval] long *plCount);
		[propget, id(didItem), helpstring("property Item")] HRESULT Item(VARIANT varIndex, [out, retval] IGuideDataProvider * *ppdataprovider);
		[propget, id(didItemWithID), helpstring("property ItemWithID")] HRESULT ItemWithID(long id, [out, retval] IGuideDataProvider * *ppdataprovider);
		[propget, id(didItemWithName), helpstring("property ItemWithName")] HRESULT ItemWithName(BSTR bstrName, [out, retval] IGuideDataProvider **ppdataprovider);
		[propget, id(didItemsWithMetaProp), helpstring("property ItemsWithMetaProperty")] HRESULT ItemsWithMetaProperty(IMetaProperty *pprop, [out, retval] IGuideDataProviders * *ppdataproviders);
		[propget, id(didItemsWithCond), helpstring("property ItemsWithMetaPropertyCond")] HRESULT ItemsWithMetaPropertyCond(IMetaPropertyCondition *pcond, [out, retval] IGuideDataProviders * *ppdataproviders);
		[propget, id(didAddNew), helpstring("property AddNew")] HRESULT AddNew([in] BSTR bstrName, [out, retval] IGuideDataProvider * *ppdataprovider);

		[propget, id(didItemsByKey), helpstring("property ItemsByKey")] HRESULT ItemsByKey([in] IMetaPropertyType *pproptype, [in] IGuideDataProvider *pprovider, [in] long idLang, [in] long vt, [out, retval] IGuideDataProviders * *ppproviders);
		[propget, id(didItemWithKey), helpstring("property ItemWithKey")] HRESULT ItemWithKey(VARIANT varIndex, [out, retval] IGuideDataProvider * *ppprovider);
		[id(didResync), helpstring("method Resync")] HRESULT Resync();
	};
	[
		uuid(3CFC7A68-76E9-4F1D-8ECE-08C44F4FFC3E),
		helpstring("GuideDataProvider Class")
	]
	coclass GuideDataProvider
	{
		[default] interface IGuideDataProvider;
	};
	[
		uuid(850A646E-140B-43B0-A243-20CC6B9FA8BC),
		helpstring("IGuideDataProvidersEvents Interface")
	]
	dispinterface IGuideDataProvidersEvents
	{
		properties:
		methods:
		[id(didItemAdded), helpstring("method ItemAdded")] HRESULT ItemAdded(IChannelLineup *pchanlineup);
		[id(didItemRemoved), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] long idObj);
		[id(didItemChanged), helpstring("method ItemChanged")] HRESULT ItemChanged(IChannelLineup *pchanlineup);
		[id(didItemsChanged), helpstring("method ItemsChanged")] HRESULT ItemsChanged();
	};

	[
		uuid(6E30077E-2E0B-4D4A-92B0-CDB5E5116E3B),
		helpstring("GuideDataProviders Class")
	]
	coclass GuideDataProviders
	{
		[default] interface IGuideDataProviders;
		[default, source] dispinterface IGuideDataProvidersEvents;
	};
};
