//------------------------------------------------------------------------------
// File: BDAIface.idl
//
// Desc: This file defines the Ring 3 BDA interfaces that are common to
//       all BDA network and device types.
//
//       The interfaces specific to a particular Network Type or filter
//       implementation are defined in a separate include file for that
//       Network Type or filter implementation.
//
// Copyright (c) 1999 - 2002, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------


//---------------------------------------------------------------------
// IUnknown import idl
//---------------------------------------------------------------------
#ifndef DO_NO_IMPORTS
import "unknwn.idl";
import "strmif.idl";
import "BdaTypes.h";
#endif

//---------------------------------------------------------------------
//
//  IBDA_NetworkProvider interface
//
//  Implemented by a BDA Network Provider
//
//      Used by a BDA device filter to register itself with
//      a Network Provider and query information about the
//      the current tuning request.
//
//---------------------------------------------------------------------
[
    object,
    uuid(fd501041-8ebe-11ce-8183-00aa00577da2),
    pointer_default(unique)
]

interface IBDA_NetworkProvider : IUnknown
{

    HRESULT
    PutSignalSource (
        [in] ULONG          ulSignalSource
        );


    HRESULT
    GetSignalSource (
        [in, out] ULONG *   pulSignalSource
        );

    HRESULT
    GetNetworkType (
        [in, out] GUID *    pguidNetworkType
        );

    HRESULT
    PutTuningSpace (
        [in] REFGUID        guidTuningSpace
        );

    HRESULT
    GetTuningSpace (
        [in, out] GUID *    pguidTuingSpace
        );

    HRESULT
    RegisterDeviceFilter (
        [in]  IUnknown *    pUnkFilterControl,
        [in, out] ULONG *   ppvRegisitrationContext
        );

    HRESULT
    UnRegisterDeviceFilter (
        [in] ULONG          pvRegistrationContext
        );

}


//---------------------------------------------------------------------
//
//  IBDA_EthernetFilter interface
//
//  Implemented by a BDA Network Provider
//
//      Used by an Ethernet Network Data Sink filter (eg. IPSink) to
//      request that the Network Provider make its best effort to tune
//      to the stream(s) on which a list of Ethernet multicast addresses
//      may be transmitted.
//
//      Addresses in the address list are byte aligned in Network order.
//      UlcbAddresses will always be an integer multiple of the
//      size of an ethernet address.
//
//---------------------------------------------------------------------
[
    object,
    uuid(71985F43-1CA1-11d3-9CC8-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_EthernetFilter : IUnknown
{
    HRESULT
    GetMulticastListSize (
        [in, out] ULONG *                   pulcbAddresses
        );

    HRESULT
    PutMulticastList (
        [in] ULONG                          ulcbAddresses,
        [in, size_is(ulcbAddresses)] BYTE   pAddressList []
        );

    HRESULT
    GetMulticastList (
        [in, out] ULONG *                       pulcbAddresses,
        [out, size_is(*pulcbAddresses)] BYTE    pAddressList []
        );

    HRESULT
    PutMulticastMode (
        [in] ULONG      ulModeMask
        );

    HRESULT
    GetMulticastMode (
        [out] ULONG *   pulModeMask
        );

}



//---------------------------------------------------------------------
//
//  IBDA_IPV4Filter interface
//
//  Implemented by a BDA Network Provider
//
//      Used by an IPv4 Network Data Sink filter to request
//      that the Network Provider make its best effort to tune
//      to the stream(s) on which a list of IPv4 multicast addresses
//      may be transmitted.
//
//      Addresses in the address list are byte aligned in Network order.
//      UlcbAddresses will always be an integer multiple of the
//      size of an IPv4 address.
//
//---------------------------------------------------------------------
[
    object,
    uuid(71985F44-1CA1-11d3-9CC8-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_IPV4Filter : IUnknown
{

    HRESULT
    GetMulticastListSize (
        [in, out] ULONG *                   pulcbAddresses
        );

    HRESULT
    PutMulticastList (
        [in] ULONG                          ulcbAddresses,
        [in, size_is(ulcbAddresses)] BYTE   pAddressList []
        );

    HRESULT
    GetMulticastList (
        [in, out] ULONG *                       pulcbAddresses,
        [out, size_is(*pulcbAddresses)] BYTE    pAddressList []
        );

    HRESULT
    PutMulticastMode (
        [in] ULONG  ulModeMask
        );

    HRESULT
    GetMulticastMode (
        [out] ULONG* pulModeMask
        );
}



//---------------------------------------------------------------------
//
//  IBDA_IPV6Filter interface
//
//  Implemented by a BDA Network Provider
//
//      Used by an IPv6 Network Data Sink filter to request
//      that the Network Provider make its best effort to tune
//      to the stream(s) on which a list of IPv6 multicast addresses
//      may be transmitted.
//
//      Addresses in the address list are byte aligned in Network order.
//      UlcbAddresses will always be an integer multiple of the
//      size of an IPv6 address.
//
//---------------------------------------------------------------------
[
    object,
    uuid(E1785A74-2A23-4fb3-9245-A8F88017EF33),
    pointer_default(unique)
]

interface IBDA_IPV6Filter : IUnknown
{

    HRESULT
    GetMulticastListSize (
        [in, out] ULONG *                   pulcbAddresses
        );

    HRESULT
    PutMulticastList (
        [in] ULONG                          ulcbAddresses,
        [in, size_is(ulcbAddresses)] BYTE   pAddressList []
        );

    HRESULT
    GetMulticastList (
        [in, out] ULONG *                       pulcbAddresses,
        [out, size_is(*pulcbAddresses)] BYTE    pAddressList []
        );

    HRESULT
    PutMulticastMode (
        [in] ULONG  ulModeMask
        );

    HRESULT
    GetMulticastMode (
        [out] ULONG* pulModeMask
        );
}



//---------------------------------------------------------------------
//
//  IBDA_DeviceControl interface
//
//  Implemented by a BDA Device Filter
//
//      Used by the Network Provider to commit a series of changes
//      on a BDA device filter.  The device filter validates and
//      accumulates all changes requested after StartChanges().  It
//      effects the accumulated list of changes when CommitChanges() is
//      called.
//
//---------------------------------------------------------------------
[
    object,
    uuid(FD0A5AF3-B41D-11d2-9C95-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_DeviceControl : IUnknown
{
    HRESULT
    StartChanges (
        void
        );

    HRESULT
    CheckChanges (
        void
        );

    HRESULT
    CommitChanges (
        void
        );

    HRESULT
    GetChangeState (
        [in, out] ULONG *   pState
        );

}



//---------------------------------------------------------------------
//
//  IBDA_PinControl interface
//
//  Implemented by a BDA Device Filter's Pin
//
//      Used by the Network Provider to determine the BDA PinID and
//      PinType on a BDA Filter's Pin
//
//---------------------------------------------------------------------
[
    object,
    uuid(0DED49D5-A8B7-4d5d-97A1-12B0C195874D),
    pointer_default(unique)
]

interface IBDA_PinControl : IUnknown
{
    HRESULT
    GetPinID (
        [in, out] ULONG *   pulPinID
        );

    HRESULT
    GetPinType (
        [in, out] ULONG *   pulPinType
        );

    HRESULT
    RegistrationContext (
        [in, out] ULONG *   pulRegistrationCtx
        );
}



//---------------------------------------------------------------------
//
//  IBDA_SignalProperties interface
//
//  Implemented by a BDA Device Filter
//
//      BDA Signal Properties is used by a Network Provider to inform
//      a BDA Device Filter about the current tuning request.  The
//      Network Provider will call the Put functions when the BDA
//      device is first registered with the Network Provider and whenever
//      the current tuning request is modified.
//
//---------------------------------------------------------------------
[
    object,
    uuid(D2F1644B-B409-11d2-BC69-00A0C9EE9E16),
    pointer_default(unique)
]

interface IBDA_SignalProperties : IUnknown
{
    HRESULT
    PutNetworkType (
        [in] REFGUID        guidNetworkType
        );

    HRESULT
    GetNetworkType (
        [in, out] GUID *    pguidNetworkType
        );

    HRESULT
    PutSignalSource (
        [in] ULONG          ulSignalSource
        );

    HRESULT
    GetSignalSource (
        [in, out] ULONG *   pulSignalSource
        );

    HRESULT
    PutTuningSpace (
        [in] REFGUID        guidTuningSpace
        );

    HRESULT
    GetTuningSpace (
        [in, out] GUID *    pguidTuingSpace
        );
}


//---------------------------------------------------------------------
//
//  IBDA_SignalStatistics interface
//
//  Implemented by a BDA Control Node
//
//      A BDA Control Node may return these properties to describe
//      the condition of a signal that is being received.
//      
//      
//      
//
//---------------------------------------------------------------------
[
    object,
    uuid(1347D106-CF3A-428a-A5CB-AC0D9A2A4338),
    pointer_default(unique)
]

interface IBDA_SignalStatistics : IUnknown
{
    HRESULT
    put_SignalStrength (
        [in] LONG           lDbStrength
        );
    
    HRESULT
    get_SignalStrength (
        [in, out] LONG *    plDbStrength
        );
    
    HRESULT
    put_SignalQuality (
        [in] LONG           lPercentQuality
        );
    
    HRESULT
    get_SignalQuality (
        [in, out] LONG *    plPercentQuality
        );
    
    HRESULT
    put_SignalPresent (
        [in] BOOLEAN        fPresent
        );
    
    HRESULT
    get_SignalPresent (
        [in, out] BOOLEAN * pfPresent
        );
    
    HRESULT
    put_SignalLocked (
        [in] BOOLEAN        fLocked
        );
    
    HRESULT
    get_SignalLocked (
        [in, out] BOOLEAN * pfLocked
        );
    
    HRESULT
    put_SampleTime (
        [in] LONG           lmsSampleTime
        );
    
    HRESULT
    get_SampleTime (
        [in, out] LONG *    plmsSampleTime
        );
}


//---------------------------------------------------------------------
//
//  IBDA_Topology interface
//
//  Implemented by a BDA Device Filter
//
//      Used by the Network Provider to query a BDA Device Filter's
//      possible topologies (template topology) and to configure
//      the device with an appropriate topology for the current
//      tuning request.  It is also used to get an IUnknown to
//      a control node which may be used to set specific tuning
//      information.
//
//---------------------------------------------------------------------
[
    object,
    uuid(79B56888-7FEA-4690-B45D-38FD3C7849BE),
    pointer_default(unique)
]

interface IBDA_Topology : IUnknown
{
    HRESULT
    GetNodeTypes (
        [in, out] ULONG *                           pulcNodeTypes,
        [in]  ULONG                                 ulcNodeTypesMax,
        [in, out, size_is (ulcNodeTypesMax)] ULONG  rgulNodeTypes[]
        );
        
    HRESULT
    GetNodeDescriptors (
        [in, out] ULONG *   ulcNodeDescriptors,
        [in]  ULONG         ulcNodeDescriptorsMax,
        [in, out, size_is (ulcNodeDescriptorsMax)] BDANODE_DESCRIPTOR   rgNodeDescriptors[]
        );

    HRESULT
    GetNodeInterfaces (
        [in]  ULONG                                     ulNodeType,
        [in, out] ULONG *                               pulcInterfaces,
        [in]  ULONG                                     ulcInterfacesMax,
        [in, out, size_is (ulcInterfacesMax)] GUID      rgguidInterfaces[]
        );

    HRESULT
    GetPinTypes (
        [in, out] ULONG *                           pulcPinTypes,
        [in]  ULONG                                 ulcPinTypesMax,
        [in, out, size_is (ulcPinTypesMax)] ULONG   rgulPinTypes[]
        );

    HRESULT
    GetTemplateConnections (
        [in, out] ULONG *                       pulcConnections,
        [in]  ULONG                             ulcConnectionsMax,
        [in, out, size_is (ulcConnectionsMax)] BDA_TEMPLATE_CONNECTION    rgConnections[]
        );

    HRESULT
    CreatePin (
        [in]  ULONG         ulPinType,
        [in, out] ULONG *   pulPinId
        );

    HRESULT
    DeletePin (
        [in] ULONG      ulPinId
        );

    HRESULT
    SetMediaType (
        [in]  ULONG             ulPinId,
        [in]  AM_MEDIA_TYPE *   pMediaType
        );

    HRESULT
    SetMedium (
        [in] ULONG          ulPinId,
        [in] REGPINMEDIUM * pMedium
        );

    HRESULT
    CreateTopology (
        [in] ULONG ulInputPinId,
        [in] ULONG ulOutputPinId
        );

    HRESULT
    GetControlNode (
        [in] ULONG              ulInputPinId,
        [in] ULONG              ulOutputPinId,
        [in] ULONG              ulNodeType,
        [in, out] IUnknown **   ppControlNode
        );
}

//---------------------------------------------------------------------
// IBDA_VoidTransform interface
//---------------------------------------------------------------------
[
    object,
    uuid(71985F46-1CA1-11d3-9CC8-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_VoidTransform : IUnknown
{
    HRESULT
    Start (
        void
        );

    HRESULT
    Stop (
        void
        );

}

//---------------------------------------------------------------------
// IBDA_NullTransform interface
//---------------------------------------------------------------------
[
    object,
    uuid(DDF15B0D-BD25-11d2-9CA0-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_NullTransform : IUnknown
{
    HRESULT
    Start (
        void
        );

    HRESULT
    Stop (
        void
        );

}


//---------------------------------------------------------------------
// IBDA_FrequencyFilter interface
//---------------------------------------------------------------------
[
    object,
    uuid(71985F47-1CA1-11d3-9CC8-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_FrequencyFilter : IUnknown
{
    HRESULT
    put_Autotune (
        [in] ULONG                  ulTransponder
        );
    
    HRESULT
    get_Autotune (
        [in, out] ULONG *           pulTransponder
        );

    HRESULT
    put_Frequency (
        [in] ULONG                  ulFrequency
        );

    HRESULT
    get_Frequency (
        [in, out] ULONG *           pulFrequency
        );

    HRESULT
    put_Polarity (
        [in] Polarisation           Polarity
        );

    HRESULT
    get_Polarity (
        [in, out] Polarisation *    pPolarity
        );

    HRESULT
    put_Range (
        [in] ULONG                  ulRange
        );

    HRESULT
    get_Range (
        [in, out] ULONG *           pulRange
        );

    HRESULT
    put_Bandwidth (
        [in] ULONG                  ulBandwidth
        );

    HRESULT
    get_Bandwidth (
        [in, out] ULONG *           pulBandwidth
        );

    HRESULT
    put_FrequencyMultiplier (
        [in] ULONG                  ulMultiplier
        );

    HRESULT
    get_FrequencyMultiplier (
        [in, out] ULONG *           pulMultiplier
        );
}


//---------------------------------------------------------------------
// IBDA_LNBInfo interface
//---------------------------------------------------------------------
[
    object,
    uuid(992CF102-49F9-4719-A664-C4F23E2408F4),
    pointer_default(unique)
]

interface IBDA_LNBInfo : IUnknown
{
    HRESULT
    put_LocalOscilatorFrequencyLowBand (
        [in] ULONG          ulLOFLow
        );

    HRESULT
    get_LocalOscilatorFrequencyLowBand (
        [in, out] ULONG *   pulLOFLow
        );

    HRESULT
    put_LocalOscilatorFrequencyHighBand (
        [in] ULONG          ulLOFHigh
        );

    HRESULT
    get_LocalOscilatorFrequencyHighBand (
        [in, out] ULONG *   pulLOFHigh
        );

    HRESULT
    put_HighLowSwitchFrequency (
        [in] ULONG          ulSwitchFrequency
        );

    HRESULT
    get_HighLowSwitchFrequency (
        [in, out] ULONG *   pulSwitchFrequency
        );
}


//---------------------------------------------------------------------
// IBDA_AutoDemodulate interface
//---------------------------------------------------------------------
[
    object,
    uuid(DDF15B12-BD25-11d2-9CA0-00C04F7971E0),
    pointer_default(unique)
]

interface IBDA_AutoDemodulate : IUnknown
{
    HRESULT
    put_AutoDemodulate (
        void
        );
}

//---------------------------------------------------------------------
// IBDA_DigitalDemodulator interface
//---------------------------------------------------------------------
[
    object,
    uuid(EF30F379-985B-4d10-B640-A79D5E04E1E0),
    pointer_default(unique)
]

interface IBDA_DigitalDemodulator : IUnknown
{
    HRESULT
    put_ModulationType (
        [in] ModulationType *   pModulationType
        );

    HRESULT
    get_ModulationType (
        [in, out] ModulationType *  pModulationType
        );

    HRESULT
    put_InnerFECMethod (
        [in] FECMethod *    pFECMethod
        );

    HRESULT
    get_InnerFECMethod (
        [in, out] FECMethod *   pFECMethod
        );

    HRESULT
    put_InnerFECRate (
        [in] BinaryConvolutionCodeRate *    pFECRate
        );

    HRESULT
    get_InnerFECRate (
        [in, out] BinaryConvolutionCodeRate *   pFECRate
        );

    HRESULT
    put_OuterFECMethod (
        [in] FECMethod *    pFECMethod
        );

    HRESULT
    get_OuterFECMethod (
        [in, out] FECMethod *   pFECMethod
        );

    HRESULT
    put_OuterFECRate (
        [in] BinaryConvolutionCodeRate *    pFECRate
        );

    HRESULT
    get_OuterFECRate (
        [in, out] BinaryConvolutionCodeRate *   pFECRate
        );

    HRESULT
    put_SymbolRate (
        [in] ULONG *    pSymbolRate
        );

    HRESULT
    get_SymbolRate (
        [in, out] ULONG *   pSymbolRate
        );

    HRESULT
    put_SpectralInversion (
        [in] SpectralInversion *    pSpectralInversion
        );

    HRESULT
    get_SpectralInversion (
        [in, out] SpectralInversion *   pSpectralInversion
        );
}

typedef enum
{
    KSPROPERTY_IPSINK_MULTICASTLIST,
    KSPROPERTY_IPSINK_ADAPTER_DESCRIPTION,
    KSPROPERTY_IPSINK_ADAPTER_ADDRESS

} KSPROPERTY_IPSINK;



//---------------------------------------------------------------------
// IBDA_IPSinkControl interface  (mutlimedia\filters.ks\ipsink)
// IBDA_IPSinkInfo    interface
//
//  IBDA_IPSinkControl is no longer being supported for Ring3 clients.
//  Use the BDA_IPSinkInfo interface instead.
//---------------------------------------------------------------------

[
    object,
    uuid(3F4DC8E2-4050-11d3-8F4B-00C04F7971E2),
    pointer_default(unique),
    helpstring("Not supported - Use IBDA_IPSinkInfo instead")
]
interface IBDA_IPSinkControl : IUnknown
{
    HRESULT GetMulticastList (
                [in, out] unsigned long *pulcbSize,
                [in, out] BYTE **pbBuffer
    );


    HRESULT GetAdapterIPAddress (
                [in,out] unsigned long *pulcbSize,
                [in,out] BYTE **pbBuffer
    );

}

[
    object,
    uuid(A750108F-492E-4d51-95F7-649B23FF7AD7),
    pointer_default(unique)
]
interface IBDA_IPSinkInfo : IUnknown
{
    HRESULT get_MulticastList (                                             // returns  N 6-byte 802.3 IP addreses.
                [in, out] ULONG *                       pulcbAddresses,     // 6*N
                [out, size_is(*pulcbAddresses)] BYTE    **ppbAddressList    // Allocated by caller, must deallocate in callee with CoTaskMemFree()
    );

    HRESULT get_AdapterIPAddress (
                [out] BSTR *pbstrBuffer
    );

    HRESULT get_AdapterDescription (
                [out] BSTR *pbstrBuffer
    );
}
//
//  mpeg-2 demultiplexer-specific interfaces follow
//

//---------------------------------------------------------------------
// IEnumPIDMap interface
//---------------------------------------------------------------------

#ifdef REMOVE_THESE
typedef enum {
    MEDIA_TRANSPORT_PACKET,         //  complete TS packet e.g. pass-through mode
    MEDIA_ELEMENTARY_STREAM,        //  PES payloads; audio/video only
    MEDIA_MPEG2_PSI,                //  PAT, PMT, CAT, Private
    MEDIA_TRANSPORT_PAYLOAD         //  gathered TS packet payloads (PES packets, etc...)
} MEDIA_SAMPLE_CONTENT ;

typedef struct {
    ULONG                   ulPID ;
    MEDIA_SAMPLE_CONTENT    MediaSampleContent ;
} PID_MAP ;
#endif // REMOVE_THESE

[
    object,
    uuid (afb6c2a2-2c41-11d3-8a60-0000f81e0e4a),
    pointer_default(unique)
]
interface IEnumPIDMap : IUnknown
{
    HRESULT
    Next (
        [in]                            ULONG       cRequest,
        [in, out, size_is (cRequest)]   PID_MAP *   pPIDMap,
        [out]                           ULONG *     pcReceived
        ) ;

    HRESULT
    Skip (
        [in]    ULONG   cRecords
        ) ;

    HRESULT
    Reset (
        ) ;

    HRESULT
    Clone (
        [out]   IEnumPIDMap **  ppIEnumPIDMap
        ) ;
} ;

//---------------------------------------------------------------------
// IMPEG2PIDMap interface
//---------------------------------------------------------------------

[
    object,
    uuid (afb6c2a1-2c41-11d3-8a60-0000f81e0e4a),
    pointer_default(unique)
]
interface IMPEG2PIDMap : IUnknown
{
    HRESULT
    MapPID (
        [in]    ULONG                   culPID,
        [in]    ULONG *                 pulPID,
        [in]    MEDIA_SAMPLE_CONTENT    MediaSampleContent
        ) ;

    HRESULT
    UnmapPID (
        [in]    ULONG   culPID,
        [in]    ULONG * pulPID
        ) ;

    HRESULT
    EnumPIDMap (
        [out]   IEnumPIDMap ** pIEnumPIDMap
        ) ;
} ;

//---------------------------------------------------------------------
// IFrequencyMap interface
// Currently implemented on the TIF. The interface can be QIed on the NP
//---------------------------------------------------------------------


    [
         object,
	 uuid(06FB45C1-693C-4ea7-B79F-7A6A54D8DEF2),
	 helpstring("IFrequencyMap Interface"),
	 pointer_default(unique),
        	 hidden, restricted
    ]

    interface IFrequencyMap : IUnknown
    {
	[helpstring("method get_FrequencyMapping")] 
	HRESULT 
	get_FrequencyMapping(
			     [out] ULONG* ulCount,
			     [out, size_is(1, *ulCount)] ULONG** ppulList
			     );
	[helpstring("method put_FrequencyMapping")] 
	HRESULT 
	put_FrequencyMapping(
			     [in] ULONG ulCount,
			     [in, size_is(ulCount)] ULONG pList[]
			     );
	[helpstring("method get_CountryCode")] 
	HRESULT 
	get_CountryCode(
			[out] ULONG *pulCountryCode
			);
	[helpstring("method put_CountryCode")] 
	HRESULT 
	put_CountryCode(
			[in] ULONG ulCountryCode
			);

       [helpstring("method get_DefaultFrequencyMapping")]
       HRESULT
       get_DefaultFrequencyMapping(
            [in] ULONG ulCountryCode,
            [out] ULONG* pulCount,
            [out, size_is(1, *pulCount)] ULONG** ppulList
            );
  
 
 	[helpstring("method get_CountryCodeList")]
    	HRESULT
    	get_CountryCodeList(
            [out] ULONG* pulCount,
            [out, size_is(1, *pulCount)] ULONG** ppulList
            );


    };



