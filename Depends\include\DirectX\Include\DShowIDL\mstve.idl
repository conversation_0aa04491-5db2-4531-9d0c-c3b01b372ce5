// Copyright (c) 1999,2000,2001  Microsoft Corporation.  All Rights Reserved.
// MSTvE.idl : IDL source for MSTvE.dll
//
// This file will be processed by the MIDL tool to
// produce the type library (TveContr.tlb) and marshalling code.
//
//		Need to put MIDL attributes of:
//			'noncreatable' on
//			'hidden'       on
//			'nonbrowsable' on
//
//  -- _Helper interfaces need to be in the library section or else
//		can't seen them in the client
//
//	-- None of the _Helper interfaces are marked dual.  Hence they 
//	   won't work up on the client... (???)
//
// ---------------------------------------------------
//			CAUTION - if Change methods below, need to regen the proxy-stub file
//				nmake -f MSTvEps.mk
//				regsvr32 /s MSTvEps.dll
//	
//
//			Also - if change any of the IID's, need to rebuild ..\uuid\[]\strmiids.lib

cpp_quote("//+-------------------------------------------------------------------------")
cpp_quote("//")
cpp_quote("//  Microsoft Windows")
cpp_quote("//  Copyright (C) Microsoft Corporation, 1999, 2000.")
cpp_quote("//")
cpp_quote("//--------------------------------------------------------------------------")
cpp_quote("#pragma once")

import "oaidl.idl";
import "ocidl.idl";

		// forward declarations
interface ITVETrigger;
interface ITVETrack;
interface ITVEVariation;
interface ITVEEnhancement;
interface ITVEService;
interface ITVEActiveService;
interface ITVESupervisor;
interface ITVEAttrMap;
interface ITVEAttrTimeQ;

interface ITVETracks;
interface ITVEVariations;
interface ITVEEnhancements;
interface ITVEServices;

interface ITVEFile;

interface ITVESupervisor_Helper;

//  Listener..
interface ITVEMCast;
interface ITVEMCasts;
interface ITVEMCastManager;
interface ITVEMCastCallback;

//  interfaces to configure the filter and get events from,
interface ITVEFilter ;
interface ITVEFilter_Helper;

// glue stuff
interface	ITVENavAid;
interface	ITVENavAid_Helper;
interface	ITVENavAid_NoVidCtl;
interface	ITVETriggerCtrl;
interface	ITVETriggerCtrl_Helper;

interface	ITVEFeature;
interface	ITVESupervisorGITProxy;
// ----------------------------------------------------------------------------------

#define COLLECTID_Item		DISPID_VALUE	// (0)	
#define COLLECTID_Count		1				// standard set of collection interfaces
#define COLLECTID_Add		2
#define COLLECTID_Remove	3
#define COLLECTID_RemoveAll	4				// extra for maps
#define COLLECTID_Key		5	
#define COLLECTID_Replace	6		
#define COLLECTID_Insert	7		
#define COLLECTID_Update	8
	
typedef enum NENH_Mode
{
	NENH_New,
	NENH_Duplicate,
	NENH_Updated,
	NENH_Starting,
	NENH_Expired
} NENH_Mode;

typedef enum NPKG_Mode
{
	NPKG_Starting,							// brand new packet (never seen this UUID before)
	NPKG_Received,							// correctly received and decoded a package
	NPKG_Duplicate,							// duplicate send of a one already successfully received (packet 0 only)
	NPKG_Resend,							// resend of one that wasn't received correctly before (packet 0 only)
	NPKG_Expired
} NPKG_Mode;

typedef enum NTRK_Mode
{
	NTRK_New,
	NTRK_Duplicate,							// exact duplicate of existing one
	NTRK_Updated,							// updated version of existing one
	NTRK_Starting,							// (not used???)
	NTRK_Expired
} NTRK_Mode;

typedef enum NFLE_Mode
{
	NFLE_Received,							// correctly received and decoded a package
	NFLE_Expired
} NFLE_Mode;

typedef enum NTUN_Mode
{
	NTUN_New,								// new station (service) never seen before
	NTUN_Retune,							// tuning back to an existing station
	NTUN_Reactivate,						// retuning to the current station
	NTUN_Turnoff,							// turning atvef off
	NTUN_Fail								// failed to tune for some reason
} NTUN_Mode;


typedef enum NWHAT_Mode						// used in NotifyAuxInfo
{
	NWHAT_Announcement,
	NWHAT_Trigger,
	NWHAT_Data,
	NWHAT_Other,
	NWHAT_Extra
} NWHAT_Mode;


		

			// ---------------------------------------------------------------------

	[
		object,
		uuid(05500101-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVETrigger Interface"),
		pointer_default(unique)
	]
	interface ITVETrigger : IDispatch
	{
		[propget, id(1), helpstring("property Parent")]			HRESULT Parent([out, retval] IUnknown* *pVal);
		[propget, id(2), helpstring("property Service")]		HRESULT Service([out, retval] ITVEService* *pVal);
		[propget, id(3), helpstring("property IsValid")]		HRESULT IsValid([out, retval] VARIANT_BOOL *pVal);
		[propget, id(4), helpstring("property URL")]			HRESULT URL([out, retval] BSTR *pVal);
		[propget, id(5), helpstring("property Name")]			HRESULT Name([out, retval] BSTR *pVal);
		[propget, id(6), helpstring("property Expires")]		HRESULT Expires([out, retval] DATE *pVal);
		[propget, id(7), helpstring("property Executes")]		HRESULT Executes([out, retval] DATE *pVal);
		[propget, id(8), helpstring("property Script")]			HRESULT Script([out, retval] BSTR *pVal);
		[propget, id(9), helpstring("property TVELevel")]		HRESULT TVELevel([out, retval] float *pVal);
		[propget, id(10), helpstring("property Rest")]			HRESULT Rest([out, retval] BSTR *pVal);
		[		  id(11), helpstring("method ParseTrigger")]	HRESULT ParseTrigger([in] const BSTR rVal);
	};

	[
		object,
		uuid(05500201-FAA5-4df9-8246-BFC23AC5CEA8),
//		dual,
		hidden,
		helpstring("ITVETrigger_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVETrigger_Helper : IUnknown
	{
		[         id(1), helpstring("method ConnectParent")]	HRESULT ConnectParent([in] ITVETrack *pTrack);
		[propget, id(2), helpstring("property CRC")]			HRESULT CRC([in] const BSTR rVal, [out, retval] BSTR *pbstrCRC);
		[		  id(3), helpstring("method UpdateFrom")]		HRESULT UpdateFrom([in] ITVETrigger *pTrigger, [out] long *plgrfTRKChanged);
		[		  id(4), helpstring("method RemoveYourself")]	HRESULT	RemoveYourself();
		[		  id(5), helpstring("method DumpToBSTR")]		HRESULT DumpToBSTR([out] BSTR *pbstrBuff);
	};


// ---------------------------------------------------

	[
		object,
		uuid(05500102-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVETrack Interface"),
		pointer_default(unique)
	]
	interface ITVETrack : IDispatch
	{
		[propget, id(1), helpstring("property Parent")]			HRESULT Parent([out, retval] IUnknown* *pVal);
		[propget, id(2), helpstring("property Service")]		HRESULT Service([out, retval] ITVEService* *pVal);
		[propget, id(3), helpstring("property Trigger")]		HRESULT Trigger([out, retval] ITVETrigger* *pVal);
		[propget, id(4), helpstring("property Description")]	HRESULT Description([out, retval] BSTR *pVal);
		[propput, id(4), helpstring("property Description")]	HRESULT Description([in] BSTR newVal);
		[         id(5), helpstring("method AttachTrigger")]	HRESULT AttachTrigger([in] ITVETrigger *pTrigger);
		[         id(6), helpstring("method ReleaseTrigger")]	HRESULT ReleaseTrigger();
		[         id(7), helpstring("method CreateTrigger")]	HRESULT CreateTrigger([in] const BSTR bstrDescription);
	};

	[
		object,
		uuid(05500202-FAA5-4df9-8246-BFC23AC5CEA8),
//		dual,
		hidden,
		helpstring("ITVETrack_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVETrack_Helper : IUnknown
	{
		[id(1),	helpstring("method ConnectParent")]		HRESULT ConnectParent(ITVEVariation *pVariation);
		[id(2), helpstring("method RemoveYourself")]	HRESULT	RemoveYourself();
		[id(3), helpstring("method DumpToBSTR")]		HRESULT DumpToBSTR([out] BSTR *pbstrBuff);
	};

	[
		object,
		uuid(05500112-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVETracks Interface"),
		pointer_default(unique)
	]

	interface ITVETracks : IDispatch
	{
		[propget, id(DISPID_NEWENUM), restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),  helpstring("property Count")]		HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),   helpstring("property Item")]		HRESULT Item([in] VARIANT var, [out, retval] ITVETrack * *pVal);
		[         id(COLLECTID_Add),    helpstring("method Add")]			HRESULT Add([in] ITVETrack *pTrack);
		[         id(COLLECTID_Remove), helpstring("method Remove")]		HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")]	HRESULT RemoveAll();
		[         id(COLLECTID_Insert), helpstring("method Insert")]		HRESULT Insert([in] int iLoc, [in] ITVETrack *pTrack);
	};

// ---------------------------------------------------

	[
		object,
		uuid(05500103-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEVariation Interface"),
		pointer_default(unique)
	]
	interface ITVEVariation : IDispatch
	{
		[propget, id(1), helpstring("property Parent")]				HRESULT Parent([out, retval] IUnknown* *pVal);
		[propget, id(2), helpstring("property Service")]			HRESULT Service([out, retval] ITVEService* *pVal);
		[propget, id(3), helpstring("property Tracks")]				HRESULT Tracks([out, retval] ITVETracks* *pVal);
		[propget, id(4), helpstring("property Description")]		HRESULT Description([out, retval] BSTR *pVal);
		[propput, id(4), helpstring("property Description")]		HRESULT Description([in] BSTR newVal);

		[propget, id(5), helpstring("property IsValid")]			HRESULT IsValid([out, retval] VARIANT_BOOL *pVal);
		[propget, id(6), helpstring("property MediaName")]			HRESULT MediaName([out, retval] BSTR *newVal);
		[propget, id(7), helpstring("property MediaTitle")]			HRESULT MediaTitle([out, retval] BSTR *newVal);
		[propget, id(8), helpstring("property FileIPAdapter")]		HRESULT FileIPAdapter([out, retval] BSTR *newVal);
		[propget, id(9), helpstring("property FileIPAddress")]		HRESULT FileIPAddress([out, retval] BSTR *newVal);
		[propget, id(10), helpstring("property FilePort")]			HRESULT FilePort([out, retval] LONG *lPort);
		[propget, id(11), helpstring("property TriggerIPAdapter")]	HRESULT TriggerIPAdapter([out, retval] BSTR *newVal);
		[propget, id(12), helpstring("property TriggerIPAddress")]	HRESULT TriggerIPAddress([out, retval] BSTR *newVal);
		[propget, id(13), helpstring("property TriggerPort")]		HRESULT TriggerPort([out, retval] LONG *lPort);
		[propget, id(14),helpstring("property Languages")]			HRESULT Languages([out, retval] ITVEAttrMap* *ppVal);
		[propget, id(15),helpstring("property SDPLanguages")]		HRESULT SDPLanguages([out, retval] ITVEAttrMap* *ppVal);
		[propget, id(16),helpstring("property Bandwidth")]			HRESULT Bandwidth([out, retval] LONG *lVal);
		[propget, id(17),helpstring("property BandwidthInfo")]		HRESULT BandwidthInfo([out, retval] BSTR *newVal);
		[propget, id(18),helpstring("property Attributes")]			HRESULT Attributes([out, retval] ITVEAttrMap* *ppVal);

		[propget, id(19),helpstring("property Rest")]				HRESULT Rest([out, retval] ITVEAttrMap* *ppVal);
		[         id(20),helpstring("method Initialize")]			HRESULT Initialize([in] BSTR newVal);
	};

	[
		object,
		uuid(05500203-FAA5-4df9-8246-BFC23AC5CEA8),
//		dual,
		hidden,
		helpstring("ITVEVariation_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVEVariation_Helper : IUnknown
	{
		[			id(1),  helpstring("method ConnectParent")]			HRESULT ConnectParent([in] ITVEEnhancement *pEnhancement);
		[			id(2),  helpstring("method DefaultTo")]				HRESULT DefaultTo([in] ITVEVariation *pVariationBase);
		[			id(3),  helpstring("method SetTriggerIPAdapter")]	HRESULT SetTriggerIPAdapter([in] BSTR bstrBuff);
		[			id(4),  helpstring("method SetFileIPAdapter")]		HRESULT SetFileIPAdapter([in] BSTR bstrBuff);
		[			id(5),  helpstring("method SubParseSDP")]			HRESULT SubParseSDP(const BSTR *pbstrSDP, BOOL *pfMissingMedia);
		[			id(6),  helpstring("method ParseCBTrigger")]		HRESULT ParseCBTrigger(BSTR bstrTrig);
		[			id(7),  helpstring("method FinalParseSDP")]			HRESULT FinalParseSDP();
		[			id(8),  helpstring("method UpdateVariation")]		HRESULT UpdateVariation(ITVEVariation *pVarNew, long *plNVAR_grfChanged);
		[			id(9),  helpstring("method InitAsXOver")]			HRESULT InitAsXOver();
		[			id(11),	helpstring("method NewXOverLink")]			HRESULT NewXOverLink([in] BSTR bstrLine21Trigger);
		[			id(12),	helpstring("method RemoveYourself")]		HRESULT	RemoveYourself();
		[propput,	id(13), helpstring("property MediaTitle")]			HRESULT MediaTitle([in] BSTR bstrBuff);
		[propput,   id(14), helpstring("property IsValid")]				HRESULT IsValid([in] VARIANT_BOOL fValid);
		[			id(15), helpstring("method DumpToBSTR")]			HRESULT DumpToBSTR([out] BSTR *bstrBuff);
	};

	[
		object,
		uuid(05500113-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEVariations Interface"),
		pointer_default(unique)
	]	
	interface ITVEVariations : IDispatch
	{
		[propget, id(DISPID_NEWENUM), restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),  helpstring("property Count")]		HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),   helpstring("property Item")]		HRESULT Item([in] VARIANT var, [out, retval] ITVEVariation * *pVal);
		[         id(COLLECTID_Add),    helpstring("method Add")]			HRESULT Add([in] ITVEVariation *pEnh);
		[         id(COLLECTID_Remove), helpstring("method Remove")]		HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")]	HRESULT RemoveAll();
		[         id(COLLECTID_Insert), helpstring("method Insert")]		HRESULT Insert([in] int iLoc, [in] ITVEVariation *pEnh);
	};
// ---------------------------------------------------
	[
		object,
		uuid(05500104-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEEnhancement Interface"),
		pointer_default(unique)
	]
	interface ITVEEnhancement : IDispatch
	{
		[propget, id(1),  helpstring("property Parent")]			HRESULT Parent([out, retval] IUnknown* *pVal);
		[propget, id(2),  helpstring("property Service")]			HRESULT Service([out, retval] ITVEService* *pVal);
		[propget, id(3),  helpstring("property Variations")]		HRESULT Variations([out, retval] ITVEVariations* *pVal);

		[propget, id(4),  helpstring("property IsValid")]			HRESULT IsValid([out, retval] VARIANT_BOOL *pVal);
		[propget, id(5),  helpstring("property ProtocolVersion")]	HRESULT ProtocolVersion([out, retval] BSTR *pVal);
		[propget, id(6),  helpstring("property SessionUserName")]	HRESULT SessionUserName([out, retval] BSTR *pVal);
		[propget, id(7),  helpstring("property SessionId")]			HRESULT SessionId([out, retval] LONG *plVal);
		[propget, id(8),  helpstring("property SessionVersion")]	HRESULT SessionVersion([out, retval] LONG *plVal);
		[propget, id(9),  helpstring("property SessionIPAddress")]	HRESULT SessionIPAddress([out, retval] BSTR *pVal);
		[propget, id(10), helpstring("property SessionName")]		HRESULT SessionName([out, retval] BSTR *pVal);
		[propget, id(11), helpstring("property EmailAddresses")]	HRESULT EmailAddresses([out, retval] ITVEAttrMap* *pVal);
		[propget, id(12), helpstring("property PhoneNumbers")]		HRESULT PhoneNumbers([out, retval] ITVEAttrMap* *pVal);

		[propget, id(13), helpstring("property Description")]		HRESULT Description([out, retval] BSTR *pVal);
		[propput, id(13), helpstring("property Description")]		HRESULT Description([in] BSTR newVal);
		[propget, id(14), helpstring("property DescriptionURI")]	HRESULT DescriptionURI([out, retval] BSTR *pVal);

		[propget, id(15), helpstring("property UUID")]				HRESULT UUID([out, retval] BSTR *pVal);
		[propget, id(16), helpstring("property StartTime")]			HRESULT StartTime([out, retval] DATE *pVal);
		[propget, id(17), helpstring("property StopTime")]			HRESULT StopTime([out, retval] DATE *pVal);
		[propget, id(18), helpstring("property IsPrimary")]			HRESULT IsPrimary([out, retval] VARIANT_BOOL *pVal);

		[propget, id(19), helpstring("property Type")]				HRESULT Type([out, retval] BSTR *pVal);
		[propget, id(20), helpstring("property TveType")]			HRESULT TveType([out, retval] BSTR *pVal);
		[propget, id(21), helpstring("property TveSize")]			HRESULT TveSize([out, retval] LONG *plVal);
		[propget, id(23), helpstring("property TveLevel")]			HRESULT TveLevel([out, retval] double *pVal);
	
		[propget, id(24), helpstring("property Attributes")]		HRESULT Attributes([out, retval] ITVEAttrMap* *pVal);
		[propget, id(25), helpstring("property Rest")]				HRESULT Rest([out, retval] ITVEAttrMap* *pVal);

		[propget, id(26), helpstring("property SAPHeaderBits")]		HRESULT SAPHeaderBits([out, retval] short *pVal);
		[propget, id(27), helpstring("property SAPAuthLength")]		HRESULT SAPAuthLength([out, retval] short *pVal);
		[propget, id(28), helpstring("property SAPMsgIDHash")]		HRESULT SAPMsgIDHash([out, retval] LONG *pVal);
		[propget, id(29), helpstring("property SAPSendingIP")]		HRESULT SAPSendingIP([out, retval] BSTR *pVal);
		[propget, id(30), helpstring("property SAPAuthData")]		HRESULT SAPAuthData([out, retval] BSTR *pVal);

		[		  id(31), helpstring("method ParseAnnouncement")]	HRESULT ParseAnnouncement([in] BSTR bstrAdapter, [in] const BSTR *pbstVal, [out] long *plgrfParseError, [out] long *plLineError);
	};


	[
		object,
		uuid(05500204-FAA5-4df9-8246-BFC23AC5CEA8),
//		dual,
		hidden,
		helpstring("ITVEEnhancement_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVEEnhancement_Helper : IUnknown
	{
		[id(0), helpstring("method ConnectParent")]		HRESULT ConnectParent(ITVEService *pService);
		[id(1), helpstring("method Activate")]			HRESULT Activate();
		[id(2), helpstring("method Deactivate")]		HRESULT Deactivate();
		[id(3), helpstring("method UpdateEnhancement")] HRESULT UpdateEnhancement([in] ITVEEnhancement *pEnhNew,[out] long *plNENH_grfChanged);
		[id(4), helpstring("method InitAsXOver")]		HRESULT InitAsXOver();
		[id(5), helpstring("method NewXOverLink")]		HRESULT NewXOverLink([in] BSTR bstrLine21Trigger);
		[id(6), helpstring("method RemoveYourself")]	HRESULT	RemoveYourself();
		[id(7), helpstring("method DumpToBSTR")]		HRESULT DumpToBSTR([out] BSTR *pbstrBuff);
	};


	[
		object,
		uuid(05500114-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEEnhancements Interface"),
		pointer_default(unique)
	]

	interface ITVEEnhancements : IDispatch
	{
		[propget, id(DISPID_NEWENUM), restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),  helpstring("property Count")]		HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),   helpstring("property Item")]		HRESULT Item([in] VARIANT var, [out, retval] ITVEEnhancement * *pVal);
		[         id(COLLECTID_Add),    helpstring("method Add")]			HRESULT Add([in] ITVEEnhancement *pEnh);
		[         id(COLLECTID_Remove), helpstring("method Remove")]		HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")]	HRESULT RemoveAll();
		[         id(COLLECTID_Insert), helpstring("method Insert")]		HRESULT Insert([in] int iLoc, [in] ITVEEnhancement *pEnh);
	};
// ---------------------------------------------------

	[
		object,
		uuid(05500105-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEService Interface"),
		pointer_default(unique)
	]
	interface ITVEService : IDispatch
	{
		[propget, id(1), helpstring("property Parent")]			HRESULT Parent([out, retval] IUnknown* *pVal);
		[propget, id(2), helpstring("property Enhancements")]	HRESULT Enhancements([out, retval] ITVEEnhancements* *pVal);
		[propget, id(3), helpstring("property Description")]	HRESULT Description([out, retval] BSTR *pVal);
		[propput, id(3), helpstring("property Description")]	HRESULT Description([in] BSTR newVal);
		[		  id(4), helpstring("method Activate")]			HRESULT Activate();		// used in tuning, turns on announcement
		[		  id(5), helpstring("method Deactivate")]		HRESULT Deactivate();		// used in tuning, turns off announcement
		[propget, id(6), helpstring("property XOverLinks")]		HRESULT XOverLinks([out, retval] ITVETracks* *pVal);	// returns ITVETracks (Collection)
		[propget, id(7),helpstring("property XOverEnhancement")] HRESULT XOverEnhancement([out, retval] ITVEEnhancement* *pVal);	// special for tree view...
		[		  id(8), helpstring("method NewXOverLink")]		HRESULT NewXOverLink([in] BSTR bstrLine21Trigger);
				// Set date offset used added to dates before expiring.  
				//  Used when doing playback recorded shows, offseting expire dates by this amount
		[propget, id(9), helpstring("property ExpireOffset")]	HRESULT ExpireOffset([out, retval] DATE *pVal);
		[propput, id(9), helpstring("property ExpireOffset")]	HRESULT ExpireOffset([in] DATE newVal);
				// return the ITVEAttrTimeQ list giving dates of IUnknowns in this service are due to expire
		[propget, id(10), helpstring("property ExpireQueue")]	HRESULT ExpireQueue([out, retval] ITVEAttrTimeQ* *pVal);
				// Expires any items earlier than the specified date + ExpireOffset
				//    If ezactly zero, Date defaults to <NOW>.
		[         id(11),helpstring("method ExpireForDate")]	HRESULT ExpireForDate([in] DATE dateExpireTime);
				// Mark Service active, only one Active service currently allowed .. turns off all others.
		[propget, id(12), helpstring("IsActive")]				HRESULT IsActive([out, retval] VARIANT_BOOL *fIsActive);
				// save and restore an arbitrary property on this service... (Useful with ITVENavAid::CacheState())
		[propput, id(13), helpstring("Property")]				HRESULT Property([in] BSTR bstrPropName, BSTR bstrPropVal);
		[propget, id(13), helpstring("Property")]				HRESULT Property([in] BSTR bstrPropName, [out, retval] BSTR *pbstrPropVal);
	};

	[
		object,
		uuid(05500205-FAA5-4df9-8246-BFC23AC5CEA8),
//		dual,
		hidden,
		helpstring("ITVEService_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVEService_Helper : IUnknown
	{
		[		  id(1),  helpstring("method ConnectParent")]				HRESULT	ConnectParent([in] ITVESupervisor *pSupervisor);
		[		  id(2),  helpstring("method ParseCBAnnouncement")]			HRESULT	ParseCBAnnouncement([in] BSTR bstrFileTrigAdapter,[in] BSTR *pbstrBuff);
		[		  id(3),  helpstring("method SetAnncIPValues")]				HRESULT SetAnncIPValues([in] BSTR bstrAnncIPAdapter,[in] BSTR bstrAnncIPAddress,[in] LONG lAnncPort);
		[		  id(4),  helpstring("method GetAnncIPValues")]				HRESULT GetAnncIPValues([in] BSTR *pbstrAnncIPAdapter,[in] BSTR *pbstrAnncIPAddress,[in] LONG *plAnncPort);
		[		  id(5),  helpstring("method InitXOverEnhancement")]		HRESULT	InitXOverEnhancement();
		[		  id(6),  helpstring("method AddToExpireQueue")]			HRESULT	AddToExpireQueue([in] DATE dateExpires, [in] IUnknown *punkItem);
		[		  id(7),  helpstring("method ChangeInExpireQueue")]			HRESULT	ChangeInExpireQueue([in] DATE dateExpires, [in] IUnknown *punkItem);
		[		  id(8),  helpstring("method RemoveFromExpireQueue")]		HRESULT	RemoveFromExpireQueue([in] IUnknown *punkItem);
		[		  id(9),  helpstring("method RemoveEnhFilesFromExpireQueue")]	HRESULT	RemoveEnhFilesFromExpireQueue([in] ITVEEnhancement *pEnhancement);
		[propget, id(10), helpstring("method ExpireQueueChangeCount")]		HRESULT ExpireQueueChangeCount([out, retval] long *pChangeCount);
		[		  id(11), helpstring("method RemoveYourself")]				HRESULT	RemoveYourself();
		[		  id(12), helpstring("method DumpToBSTR")]					HRESULT	DumpToBSTR([out] BSTR *pbstrBuff);
	};


	[
		object,
		uuid(05500305-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEFeature Interface (Inherits ITVEService)"),
		pointer_default(unique)
	]
	interface ITVEFeature : ITVEService
	{
			// New methods just for the Feature
		[id(101),		helpstring("method TuneTo")]					HRESULT TuneTo([in] BSTR bstrStation, [in] BSTR bstrIPAdapter);
		[id(102),		helpstring("method ReTune")]					HRESULT ReTune([in] ITVEService *pService);
		[id(103),		helpstring("method BindToSupervisor")]			HRESULT BindToSupervisor([in] ITVESupervisor *pTVESupervisor);
			// these are incoming events called by Supervisor ((_ITVEvents).. Must match
		[id(2101),  helpstring("method NotifyTVETune")]					HRESULT NotifyTVETune([in] NTUN_Mode tuneMode, [in] ITVEService *pService, [in] BSTR bstrDescription, [in] BSTR bstrIPAdapter);
		[id(2102),  helpstring("method NotifyTVEEnhancementNew")]		HRESULT NotifyTVEEnhancementNew([in] ITVEEnhancement *pEnh);
		[id(2103),  helpstring("method NotifyTVEEnhancementUpdated")]	HRESULT NotifyTVEEnhancementUpdated([in] ITVEEnhancement *pEnh, [in] long lChangedFlags);	// changedFlags : NENH_grfDiff
		[id(2104),  helpstring("method NotifyTVEEnhancementStarting")]	HRESULT NotifyTVEEnhancementStarting([in] ITVEEnhancement *pEnh);
		[id(2105),  helpstring("method NotifyTVEEnhancementExpired")]	HRESULT NotifyTVEEnhancementExpired([in] ITVEEnhancement *pEnh);
		[id(2106),  helpstring("method NotifyTVETriggerNew")]			HRESULT NotifyTVETriggerNew([in] ITVETrigger *pTrigger,[in]  BOOL fActive);
		[id(2107),  helpstring("method NotifyTVETriggerUpdated")]		HRESULT NotifyTVETriggerUpdated([in] ITVETrigger *pTrigger,[in]  BOOL fActive, [in] long lChangedFlags);	// changedFlags : NTRK_grfDiff
		[id(2108),  helpstring("method NotifyTVETriggerExpired")]		HRESULT NotifyTVETriggerExpired([in] ITVETrigger *pTrigger,[in]  BOOL fActive);
		[id(2109),  helpstring("method NotifyTVEPackage")]				HRESULT NotifyTVEPackage([in] NPKG_Mode engPkgMode, [in] ITVEVariation *pVariation, [in] BSTR bstrUUID, [in] long  cBytesTotal, [in] long  cBytesReceived);
		[id(2110),  helpstring("method NotifyTVEFile")]					HRESULT NotifyTVEFile([in] NFLE_Mode engFileMode, [in] ITVEVariation *pVariation, [in] BSTR bstrUrlName, [in] BSTR bstrFileName);
		[id(2111),  helpstring("method NotifyTVEAuxInfo")]				HRESULT NotifyTVEAuxInfo([in] NWHAT_Mode engAuxInfoMode, [in] BSTR bstrAuxInfoString, [in] long lChangedFlags, [in] long lErrorLine);	// WhatIsIt is NWHAT_Mode - lChangedFlags is NENH_grfDiff or NTRK_grfDiff treated as error bits 

	};

	[
		object,
		uuid(05500115-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEServices Interface"),
		pointer_default(unique)
	]
	interface ITVEServices : IDispatch
	{
		[propget, id(DISPID_NEWENUM),   restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),  helpstring("property Count")]		HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),   helpstring("property Item")]		HRESULT Item([in] VARIANT var, [out, retval] ITVEService * *pVal);
		[         id(COLLECTID_Add),    helpstring("method Add")]			HRESULT Add([in] ITVEService *pService);
		[         id(COLLECTID_Remove), helpstring("method Remove")]		HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")]	HRESULT RemoveAll();
		[         id(COLLECTID_Insert), helpstring("method Insert")]		HRESULT Insert([in] int iLoc, [in] ITVEService *pService);
	};


// ---------------------------------------------------

	[
		object,
		uuid(05500106-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVESupervisor Interface"),
		pointer_default(unique)
	]

	interface ITVESupervisor : IDispatch
	{
		[propget, id(1), helpstring("property Services")]		HRESULT Services([out, retval] ITVEServices* *pVal);
		[propget, id(2), helpstring("property Description")]	HRESULT Description([out, retval] BSTR *pVal);
		[propput, id(2), helpstring("property Description")]	HRESULT Description([in] BSTR newVal);
		[		  id(3), helpstring("method TuneTo")]			HRESULT TuneTo([in] BSTR bstrDescription, [in] BSTR bstrIPAdapter);
		[		  id(4), helpstring("method ReTune")]			HRESULT ReTune([in] ITVEService *pService);

		[		  id(5), helpstring("method NewXOverLink")]		HRESULT NewXOverLink([in] BSTR bstrLine21Trigger);
				// Expires any items earlier than the specified date + service.ExpireOffset
				//    If ezactly zero, Date defaults to <NOW>.
		[         id(6), helpstring("method ExpireForDate")]	HRESULT ExpireForDate([in] DATE dateExpireTime);

				// need to be here rather than Helper, since called from Filter, and Supervisor_Helper
				//    not in the proxy-stub DLL
		[		  id(7), restricted, helpstring("method InitStats")]	HRESULT InitStats();
		[		  id(8), restricted, helpstring("method GetStats")]		HRESULT GetStats([out] BSTR *pbstrBuff);  // CTVEStats
	};

	[
		object,
		uuid(05500206-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		hidden,
		helpstring("ITVESupervisor_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVESupervisor_Helper : IUnknown
	{
		[id(1), helpstring("method ConnectParent")]				HRESULT ConnectParent([in] IUnknown *pUnk);
		[id(2), helpstring("method GetActiveService")]			HRESULT GetActiveService([out] ITVEService **ppActiveService);
		[id(3), helpstring("method GetMCastManager")]			HRESULT GetMCastManager([out] ITVEMCastManager **pMCastManager);
		[id(4), helpstring("method UnpackBuffer")]				HRESULT UnpackBuffer([in] IUnknown *pTVEVariation, [in] unsigned char *m_rgbData, [in] int cBytes);

		[id(5), helpstring("method NotifyEnhancement")]			HRESULT NotifyEnhancement([in] NENH_Mode enhMode,[in]  ITVEEnhancement *pEnhancement,[in] long lChangedFlags);	// changedFlags NENH_grfDiff
		[id(6), helpstring("method NotifyTrigger")]				HRESULT NotifyTrigger([in] NTRK_Mode trgMode,[in]  ITVETrack *pTrack,[in]  long lChangedFlags);					// changedFlags NTRK_grfDiff
		[id(7), helpstring("method NotifyPackage")]				HRESULT NotifyPackage([in] NPKG_Mode pkgMode, [in] ITVEVariation *pVariation, [in] BSTR bstrPackageUUID, [in] long cBytesTotal, [in] long cBytesReceived);
		[id(8), helpstring("method NotifyFile")]				HRESULT NotifyFile([in] NFLE_Mode fileMode, [in] ITVEVariation *pVariation, [in] BSTR bstrURLName, [in] BSTR bstrFileName);
		[id(9), helpstring("method NotifyTune")]				HRESULT NotifyTune([in] NTUN_Mode tuneMode, [in] ITVEService *pService, [in] BSTR bstrDescription, [in] BSTR bstrIPAdapter);
						// WhatIsIt is NWHAT_Mode - lChangedFlags is NENH_grfDiff or NTRK_grfDiff treated as error bits
		[id(10), helpstring("method NotifyAuxInfo")]			HRESULT NotifyAuxInfo([in] NWHAT_Mode whatMode, [in] BSTR bstrAuxInfoString, [in] long lgrfWhatDiff, [in] long lLineError);

		[id(11), helpstring("method NotifyEnhancement_XProxy")]	HRESULT NotifyEnhancement_XProxy([in] NENH_Mode enhMode,[in] ITVEEnhancement *pEnhancement,[in] long lChangedFlags);	// changedFlags NENH_grfDiff
		[id(12), helpstring("method NotifyTrigger_XProxy")]		HRESULT NotifyTrigger_XProxy([in] NTRK_Mode trgMode,[in]  ITVETrack *pTrack,[in]  long lChangedFlags);					// changedFlags NTRK_grfDiff
		[id(13), helpstring("method NotifyPackage_XProxy")]		HRESULT NotifyPackage_XProxy( NPKG_Mode pkgMode, ITVEVariation *pVariation,BSTR bstrPackageUUID, long cBytesTotal,long cBytesReceived);
		[id(14), helpstring("method NotifyFile_XProxy")]		HRESULT NotifyFile_XProxy([in] NFLE_Mode fileMode, [in] ITVEVariation *pVariation, [in] BSTR bstrURLName, [in] BSTR bstrFileName);
		[id(15), helpstring("method NotifyTune_XProxy")]		HRESULT NotifyTune_XProxy([in] NTUN_Mode tuneMode, [in] ITVEService *pService, [in] BSTR bstrDescription, [in] BSTR bstrIPAdapter);
		[id(16), helpstring("method NotifyAuxInfo_XProxy")]		HRESULT NotifyAuxInfo_XProxy([in] NWHAT_Mode whatMode, [in] BSTR bstrAuxInfoString, [in] long lgrfWhatDiff,[in] long lLineError);

		[propget, id(17),  helpstring("property HaltFlags")]	HRESULT	HaltFlags([out, retval] LONG *plGrfHaltFlags);
		[propput, id(17),  helpstring("property HaltFlags")]	HRESULT HaltFlags([in] LONG lGrfHaltFlags);

		[		  id(18),  helpstring("method RemoveAllListenersOnAdapter")]	HRESULT RemoveAllListenersOnAdapter([in] BSTR bstrAdapter);
		[propget, id(19),  helpstring("method PossibleIPAdapterAddress")]		HRESULT PossibleIPAdapterAddress([in] int iAdapter, [out,retval] BSTR *pbstrIPAdapterAddr);
		[		  id(20), helpstring("method DumpToBSTR")]				HRESULT DumpToBSTR([out] BSTR *pbstrBuff);

		[propget, id(21), helpstring("property SupervisorGITProxy")]		HRESULT SupervisorGITProxy([out] ITVESupervisorGITProxy **ppSupervisorGITProxy);		// internal GIT refcounted pointer to supervisor 
	};

	[
		hidden,
		object,
		uuid(05500306-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVESupervisorGITProxy Interface"),
		pointer_default(unique)
	]

	interface ITVESupervisorGITProxy: IUnknown
	{
		[propget, id(1), helpstring("property Supervisor")]		HRESULT Supervisor([out, retval] ITVESupervisor **ppVal);
		[propput, id(1), helpstring("property Supervisor")]		HRESULT Supervisor([in] ITVESupervisor  *pVal);
	};

// ---------------------------------------------------

			// ---------------------------------
	[
		object,
		uuid(05500121-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEAttrMap Interface"),
		pointer_default(unique)
	]
	interface ITVEAttrMap : IDispatch
	{
		[propget, id(DISPID_NEWENUM), restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),     helpstring("property Count")]   HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),      helpstring("property Item")]    HRESULT Item([in] VARIANT var, [out, retval] BSTR *pVal);
		[propget, id(COLLECTID_Key),       helpstring("property Key")]     HRESULT Key([in] VARIANT var, [out, retval] BSTR *pKey);
		[         id(COLLECTID_Add),       helpstring("method Add")]       HRESULT Add([in] BSTR bstrKey, [in] BSTR bstrValue);
		[         id(COLLECTID_Replace),   helpstring("method Replace")]   HRESULT Replace([in] BSTR bstrKey, [in] BSTR bstrValue);
		[         id(COLLECTID_Remove),    helpstring("method Remove")]    HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[         id(7),				   helpstring("method Add1")]      HRESULT Add1(BSTR bstrValue);
		[         id(8),                   helpstring("method DumpToBSTR")] HRESULT DumpToBSTR([out] BSTR *pDump);
	};

// ---------------------------------------------------

			// ---------------------------------
	[
		object,
		uuid(05500122-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEAttrTimeQ Interface"),
		pointer_default(unique)
	]
	interface ITVEAttrTimeQ : IDispatch
	{
		[propget, id(DISPID_NEWENUM), restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),     helpstring("property Count")]   HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),      helpstring("property Item")]    HRESULT Item([in] VARIANT var, [out, retval] IUnknown **ppVal);
		[propget, id(COLLECTID_Key),       helpstring("property Key")]     HRESULT Key([in] VARIANT var, [out, retval] DATE *pKey);
		[         id(COLLECTID_Add),       helpstring("method Add")]       HRESULT Add([in] DATE dateKey, [in] IUnknown *pUnk);
		[         id(COLLECTID_Remove),    helpstring("method Remove")]    HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")] HRESULT RemoveAll();
		[         id(COLLECTID_Update),    helpstring("method Update")]	   HRESULT Update([in] DATE dateKey, [in] IUnknown *pUnk);
		[         id(9),				   helpstring("method LockRead")]	 HRESULT LockRead();
		[         id(10),				   helpstring("method LockWrite")]	 HRESULT LockWrite();
		[         id(11),				   helpstring("method Unlock")]		 HRESULT Unlock();
		[         id(12),				   helpstring("method RemoveSimple")]    HRESULT RemoveSimple([in] VARIANT var);	// remove without calling var->RemoveYourself()
		[         id(13),                  helpstring("method DumpToBSTR")] HRESULT DumpToBSTR([out] BSTR *pDump);
	};

// ---------------------------------------------------
	[
		object,
		uuid(05500130-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEMCast Interface"),
		pointer_default(unique)
	]
	interface ITVEMCast : IDispatch
	{
		[propget, id(1),  helpstring("property IPAdapter")]		HRESULT IPAdapter([out, retval] BSTR *pVal);
		[propput, id(1),  helpstring("property IPAdapter")]		HRESULT IPAdapter([in] BSTR newVal);
		[propget, id(2),  helpstring("property IPAddress")]		HRESULT IPAddress([out, retval] BSTR *pVal);
		[propput, id(2),  helpstring("property IPAddress")]		HRESULT IPAddress([in] BSTR newVal);
		[propget, id(3),  helpstring("property IPPort")]		HRESULT IPPort([out, retval] long *pVal);
		[propput, id(3),  helpstring("property IPPort")]		HRESULT IPPort([in] long newVal);
		[		  id(4),  helpstring("method Join")]			HRESULT Join();
		[	      id(5),  helpstring("method Leave")]			HRESULT Leave();
		[propget, id(6),  helpstring("property IsJoined")]		HRESULT IsJoined([out, retval] VARIANT_BOOL *pVal);
		[propget, id(7),  helpstring("property IsSuspended")]	HRESULT IsSuspended([out, retval] VARIANT_BOOL *pVal);
		[		  id(8),  helpstring("method Suspend")]			HRESULT Suspend(VARIANT_BOOL fSuspend);
		[propget, id(9),  helpstring("property PacketCount")]	HRESULT PacketCount([out, retval] long *pVal);
		[propget, id(10), helpstring("property ByteCount")]		HRESULT ByteCount([out, retval] long *pVal);
		[		  id(11), helpstring("method KeepStats")]		HRESULT KeepStats(VARIANT_BOOL fKeepStats);
		[		  id(12), helpstring("method ResetStats")]		HRESULT ResetStats();
		[		  id(13), helpstring("method SetReadCallback")]	HRESULT SetReadCallback([in] int nBuffers, [in] int iPrioritySetback, [in] IUnknown *pVal);
		[		  id(14), helpstring("method ConnectManager")]	HRESULT ConnectManager([in] ITVEMCastManager* pVal);
		[propget, id(15), helpstring("property Manager")]		HRESULT Manager([out, retval] IUnknown* *ppVal);
		[propget, id(16), helpstring("property QueueThreadId")]	HRESULT QueueThreadId([out, retval] long* pVal);
		[propput, id(16), helpstring("property QueueThreadId")]	HRESULT QueueThreadId([in] long pVal);
		[propget, id(17), helpstring("property WhatType")]		HRESULT WhatType([out, retval] NWHAT_Mode *pWhatType);
		[propput, id(17), helpstring("property WhatType")]		HRESULT WhatType([in] NWHAT_Mode whatType);
	};

	[
		object,
		uuid(05500131-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEMCasts Interface"),
		pointer_default(unique)
	]
	interface ITVEMCasts : IDispatch
	{
		[propget, id(DISPID_NEWENUM), restricted, helpstring("property _NewEnum")] HRESULT _NewEnum([out, retval] IUnknown **pCollection);
		[propget, id(COLLECTID_Count),  helpstring("property Count")]		HRESULT Count([out, retval] long *pVal);
		[propget, id(COLLECTID_Item),   helpstring("property Item")]		HRESULT Item([in] VARIANT var, [out, retval] ITVEMCast * *pVal);
		[         id(COLLECTID_Add),    helpstring("method Add")]			HRESULT Add([in] ITVEMCast *punk);
		[         id(COLLECTID_Remove), helpstring("method Remove")]		HRESULT Remove([in] VARIANT var);
		[         id(COLLECTID_RemoveAll), helpstring("method RemoveAll")]	HRESULT RemoveAll();
	};
	[
		object,
		uuid(05500132-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEMCastManager Interface"),
		pointer_default(unique)
	]
	interface ITVEMCastManager : IDispatch
	{
		[propget, id(1), helpstring("property MCasts")]			HRESULT MCasts([out, retval] ITVEMCasts* *pVal);
		[propget, id(2), helpstring("property Supervisor")]		HRESULT Supervisor([out, retval] ITVESupervisor* *ppSupervisor);
		[propput, id(2), helpstring("property Supervisor")]		HRESULT Supervisor([in] ITVESupervisor *pSupervisor);
		[		  id(3), helpstring("method AddMulticast")]		HRESULT AddMulticast([in] NWHAT_Mode whatType, [in] BSTR bsAdapter, [in] BSTR bsIPAddress, [in] LONG ulIPPort, [in] LONG cBuffers, [in] IUnknown *pICallback, [out] ITVEMCast **ppMCastAdded);
		[		  id(4), helpstring("method FindMulticast")]	HRESULT FindMulticast([in] BSTR bstrIPAdapter, [in] BSTR bstrIPAddress, [in] LONG sPort,  [out] ITVEMCast **ppMCast, [out] LONG *pcMatches);
		[         id(5), helpstring("method RemoveMulticast")]  HRESULT RemoveMulticast(ITVEMCast *pMCast);
		[		  id(6), helpstring("method JoinAll")]			HRESULT JoinAll();
		[		  id(7), helpstring("method LeaveAll")]			HRESULT LeaveAll();
		[		  id(8), helpstring("method SuspendAll")]		HRESULT SuspendAll([in] VARIANT_BOOL fSuspend);
		[		  id(9), helpstring("method Lock_")]			HRESULT Lock_();
		[		  id(10), helpstring("method Unlock_")]			HRESULT Unlock_();
		[		  id(11), helpstring("method DumpStatsToBSTR")]	HRESULT DumpStatsToBSTR(int iType, BSTR *pBSTR);
		[propget, id(13), helpstring("property HaltFlags")]		HRESULT	HaltFlags([out, retval] LONG *plGrfHaltFlags);
		[propput, id(13), helpstring("property HaltFlags")]		HRESULT HaltFlags([in] LONG lGrfHaltFlags);
	};

	[
		object,
		uuid(05500232-FAA5-4df9-8246-BFC23AC5CEA8),
//		dual,
		hidden,
		helpstring("ITVEMCastManager_Helper Interface"),
		pointer_default(unique)
	]
	interface ITVEMCastManager_Helper : IUnknown
	{
		[		  id(1), helpstring("method DumpString")]			HRESULT DumpString(BSTR bstrDump);	// writes string to a Dump.txt
		[		  id(2), helpstring("method CreateQueueThread")]	HRESULT CreateQueueThread();
		[		  id(3), helpstring("method KillQueueThread")]		HRESULT KillQueueThread();
		[		  id(4), helpstring("method PostToQueueThread")]	HRESULT PostToQueueThread(UINT uiMsg, WPARAM wParam, LPARAM lParam);
		[		  id(5), helpstring("method GetPacketCounts")]		HRESULT GetPacketCounts([out] LONG *pCPackets, [out] LONG *pCPacketsDropped, [out] LONG *pCPacketsDroppedTotal);
	};


	[
		object,
		uuid(05500133-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEMCastCallback Interface"),
		pointer_default(unique)
	]
	interface ITVEMCastCallback : IDispatch
	{
		[id(1), helpstring("method SetMCast")]				HRESULT SetMCast(ITVEMCast *pMCast);
		[id(2), helpstring("method ProcessPacket")]			HRESULT ProcessPacket(unsigned char *pchBuffer, long cBytes, long lPacketId);
		[id(3), helpstring("method PostPacket")]			HRESULT PostPacket(unsigned char *pchBuffer, long cBytes, long wPacketId);
	};




					// ................ specific callbacks ..............
	[
		object,
		uuid(05500141-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVECBAnnc Interface"),
		pointer_default(unique)
	]
	interface ITVECBAnnc : IDispatch
	{
		[id(1), helpstring("method Init")] HRESULT Init(BSTR bstrFileTrigAdapter, ITVEService *pService);			// want ITVEService here
	};
	[
		object,
		uuid(05500142-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVECBTrig Interface"),
		pointer_default(unique)
	]
	interface ITVECBTrig : IDispatch
	{
		[id(1), helpstring("method Init")] HRESULT Init(ITVEVariation *pIVariation);
	};
	[
		object,
		uuid(05500143-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVECBFile Interface"),
		pointer_default(unique)
	]
	interface ITVECBFile : IDispatch
	{
		[id(1), helpstring("method Init")] HRESULT Init(ITVEVariation *pIVariation, ITVEService *pIService);
	};
	[
		object,
		uuid(05500144-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVECBDummy Interface"),
		pointer_default(unique)
	]
	interface ITVECBDummy : IDispatch
	{
		[id(1), helpstring("method Init")] HRESULT Init(int i);
	};


// ----------------------------------------------------
	[
		object,
		uuid(05500151-FAA5-4df9-8246-BFC23AC5CEA8),
		dual,
		helpstring("ITVEFile Interface"),
		pointer_default(unique)
	]
	interface ITVEFile : IDispatch
	{
		[		  id(1),  helpstring("method InitializeFile")]		HRESULT InitializeFile([in] ITVEVariation *pVaria, [in] BSTR bsName, [in] BSTR bsLoc, [in] DATE dateExpires);
		[		  id(2),  helpstring("method InitializePackage")]	HRESULT InitializePackage([in] ITVEVariation *pVaria, [in] BSTR bsName, [in] BSTR bsLoc, [in] DATE dateExpires);
		[propget, id(3),  helpstring("property Description")]		HRESULT Description([out, retval] BSTR* pBstrDesc);
		[propget, id(4),  helpstring("property Location")]			HRESULT Location([out, retval] BSTR* pBstrLoc);
		[propget, id(5),  helpstring("property ExpireTime")]		HRESULT ExpireTime([out, retval] DATE* pDateExpires);
		[propget, id(6),  helpstring("property IsPackage")]			HRESULT IsPackage([out, retval] BOOL* pfVal);
		[propget, id(7),  helpstring("property Variation")]			HRESULT Variation([out, retval] ITVEVariation* *pVal);
		[propget, id(8),  helpstring("property Service")]			HRESULT Service([out, retval] ITVEService* *pVal);
		[         id(9),  helpstring("method RemoveYourself")]		HRESULT RemoveYourself();
		[		  id(10), helpstring("method DumpToBSTR")]			HRESULT DumpToBSTR([out] BSTR *pBstrDump);
	};


// ----------------------------------------------------
//   ITVENavAid
// ----------------------------------------------------

[
    object,
	uuid (05500160-FAA5-4df9-8246-BFC23AC5CEA8),
	dual,
	helpstring("ITVENavAid Interface"),
	pointer_default(unique)
]
interface ITVENavAid : IDispatch
{
		[propput, id(1),  helpstring("property WebBrowserApp")]			HRESULT WebBrowserApp([in] IDispatch *pWebBrowser);
		[propget, id(1),  helpstring("property WebBrowserApp")]			HRESULT WebBrowserApp([out, retval] IDispatch **ppWebBrowser);
		[propget, id(2),  helpstring("property TVETriggerCtrl")]		HRESULT TVETriggerCtrl([out, retval] ITVETriggerCtrl **ppTriggerCtrl);
 		[propput, id(3),  helpstring("property EnableAutoTriggering")]	HRESULT EnableAutoTriggering([in] long lAutoTriggers);
		[propget, id(3),  helpstring("property EnableAutoTriggering")]	HRESULT EnableAutoTriggering([out, retval] long *plAutoTriggers);
		[propget, id(4),  helpstring("property ActiveVariation")]		HRESULT ActiveVariation([out, retval] ITVEVariation **ppActiveVariation);
		[propput, id(4),  helpstring("property ActiveVariation")]		HRESULT ActiveVariation([in] ITVEVariation *pActiveVariation);
		[propget, id(5),  helpstring("property TVEFeature")]			HRESULT TVEFeature([out, retval] ITVEFeature **ppTVEFeature);
		[propget, id(6),  helpstring("property CacheState")]			HRESULT CacheState([out, retval] BSTR *pbstrBuff);
		[propput, id(6),  helpstring("property CacheState")]			HRESULT CacheState([in] BSTR bstrBuff);
		[		, id(7),  helpstring("method NavUsingTVETrigger")]		HRESULT NavUsingTVETrigger([in] ITVETrigger *pTrigger, [in] long lForceNav, [in] long lForceExec);
		[		, id(8),  helpstring("method ExecScript")]				HRESULT ExecScript([in] BSTR bstrScript, [in] BSTR bstrLanguage);
		[		, id(9),  helpstring("method Navigate")]				HRESULT Navigate([in] VARIANT *URL,[in] VARIANT *Flags,[in] VARIANT *TargetFrameName, [in] VARIANT *PostData, [in] VARIANT *Headers);
		[propget, id(10), helpstring("property CurrTVEName")]			HRESULT CurrTVEName([out, retval] BSTR *pbstrName);
		[propget, id(11), helpstring("property CurrTVEURL")]			HRESULT CurrTVEURL([out, retval] BSTR *pbstrURL);

		// these are incoming sinkn events called by Supervisor ((_ITVEvents).. ID's Must match (I think)
		// &DIID__ITVEEvents
		[id(2101),  helpstring("method NotifyTVETune")]					HRESULT NotifyTVETune([in] NTUN_Mode tuneMode, [in] ITVEService *pService, [in] BSTR bstrDescription, [in] BSTR bstrIPAdapter);
		[id(2102),  helpstring("method NotifyTVEEnhancementNew")]		HRESULT NotifyTVEEnhancementNew([in] ITVEEnhancement *pEnh);
		[id(2103),  helpstring("method NotifyTVEEnhancementUpdated")]	HRESULT NotifyTVEEnhancementUpdated([in] ITVEEnhancement *pEnh, [in] long lChangedFlags);	// changedFlags : NENH_grfDiff
		[id(2104),  helpstring("method NotifyTVEEnhancementStarting")]	HRESULT NotifyTVEEnhancementStarting([in] ITVEEnhancement *pEnh);
		[id(2105),  helpstring("method NotifyTVEEnhancementExpired")]	HRESULT NotifyTVEEnhancementExpired([in] ITVEEnhancement *pEnh);
		[id(2106),  helpstring("method NotifyTVETriggerNew")]			HRESULT NotifyTVETriggerNew([in] ITVETrigger *pTrigger,[in]  BOOL fActive);
		[id(2107),  helpstring("method NotifyTVETriggerUpdated")]		HRESULT NotifyTVETriggerUpdated([in] ITVETrigger *pTrigger,[in]  BOOL fActive, [in] long lChangedFlags);	// changedFlags : NTRK_grfDiff
		[id(2108),  helpstring("method NotifyTVETriggerExpired")]		HRESULT NotifyTVETriggerExpired([in] ITVETrigger *pTrigger,[in]  BOOL fActive);
		[id(2109),  helpstring("method NotifyTVEPackage")]				HRESULT NotifyTVEPackage([in] NPKG_Mode engPkgMode, [in] ITVEVariation *pVariation, [in] BSTR bstrUUID, [in] long  cBytesTotal, [in] long  cBytesReceived);
		[id(2110),  helpstring("method NotifyTVEFile")]					HRESULT NotifyTVEFile([in] NFLE_Mode engFileMode, [in] ITVEVariation *pVariation, [in] BSTR bstrUrlName, [in] BSTR bstrFileName);
		[id(2111),  helpstring("method NotifyTVEAuxInfo")]				HRESULT NotifyTVEAuxInfo([in] NWHAT_Mode engAuxInfoMode, [in] BSTR bstrAuxInfoString, [in] long lChangedFlags, [in] long lErrorLine);	// WhatIsIt is NWHAT_Mode - lChangedFlags is NENH_grfDiff or NTRK_grfDiff treated as error bits 


		// &DIID_DWebBrowserEvents2, 

		[id(0x66),  helpstring("method NotifyStatusTextChange")]		HRESULT NotifyStatusTextChange(BSTR Text);
		[id(0x6c),  helpstring("method NotifyProgressChange")]			HRESULT NotifyProgressChange(LONG Progress, LONG ProgressMax);
		[id(0x69),  helpstring("method NotifyCommandStateChange")]		HRESULT NotifyCommandStateChange(LONG Command, VARIANT_BOOL Enable);
		[id(0x6a),  helpstring("method NotifyDownloadBegin")]			HRESULT NotifyDownloadBegin();
		[id(0x68),  helpstring("method NotifyDownloadComplete")]		HRESULT NotifyDownloadComplete();
		[id(0x71),  helpstring("method NotifyTitleChange")]				HRESULT NotifyTitleChange(BSTR Text);
		[id(0x70),  helpstring("method NotifyPropertyChange")]			HRESULT NotifyPropertyChange(BSTR szProperty);
		[id(0xfa),  helpstring("method NotifyBeforeNavigate2")]			HRESULT NotifyBeforeNavigate2(IDispatch * pDisp, VARIANT * URL, VARIANT * Flags, VARIANT * TargetFrameName, VARIANT * PostData, VARIANT * Headers, VARIANT_BOOL * Cancel);
		[id(0xfb),  helpstring("method NotifyNewWindow2")]				HRESULT NotifyNewWindow2(IDispatch * * ppDisp, VARIANT_BOOL * Cancel);
		[id(0xfc),  helpstring("method NotifyNavigateComplete2")]		HRESULT NotifyNavigateComplete2(IDispatch * pDisp, VARIANT * URL);
		[id(0x103), helpstring("method NotifyDocumentComplete")]		HRESULT NotifyDocumentComplete(IDispatch * pDisp, VARIANT * URL);
		[id(0xfd),  helpstring("method NotifyOnQuit")]					HRESULT NotifyOnQuit();
		[id(0xfe),  helpstring("method NotifyOnVisible")]				HRESULT NotifyOnVisible(VARIANT_BOOL Visible);
		[id(0xff),  helpstring("method NotifyOnToolBar")]				HRESULT NotifyOnToolBar(VARIANT_BOOL ToolBar);
		[id(0x100), helpstring("method NotifyOnMenuBar")]				HRESULT NotifyOnMenuBar(VARIANT_BOOL MenuBar);
		[id(0x101), helpstring("method NotifyOnStatusBar")]				HRESULT NotifyOnStatusBar(VARIANT_BOOL StatusBar);
		[id(0x102), helpstring("method NotifyOnFullScreen")]			HRESULT NotifyOnFullScreen(VARIANT_BOOL FullScreen);
		[id(0x104), helpstring("method NotifyOnTheaterMode")]			HRESULT NotifyOnTheaterMode(VARIANT_BOOL TheaterMode);

} ;
				// ------
				//    these interfaces for running without the VidControl
				//    - idea is to create CoCreate supervisor, and 'put' it here.
[
    object,
	uuid (05500360-FAA5-4df9-8246-BFC23AC5CEA8),
	hidden,
//	dual,
	helpstring("ITVENavAid_NoVidCtl Interface"),
	pointer_default(unique)
]
interface ITVENavAid_NoVidCtl : IUnknown
{
	[propput ,id(1),  helpstring("property NoVidCtl_Supervisor")]		HRESULT NoVidCtl_Supervisor([in] ITVESupervisor *pSuper);					
	[propget ,id(1),  helpstring("property NoVidCtl_Supervisor")]		HRESULT NoVidCtl_Supervisor([out,retval] ITVESupervisor **ppSuper);					
	
} ;

				// ------
[
    object,
	uuid (05500260-FAA5-4df9-8246-BFC23AC5CEA8),
	hidden,
	dual,											// - used in proxying, must be marked Dual regardless of what .net says
	helpstring("ITVENavAid_Helper Interface"),
	pointer_default(unique)
]
interface ITVENavAid_Helper : IUnknown
{
		[		  id(1),  helpstring("method LocateVidAndTriggerCtrls")]			HRESULT LocateVidAndTriggerCtrls([out] IDispatch **pVidCtrl, [out] IDispatch **pTrigCtrl);
		[		  id(2),  helpstring("method NotifyTVETriggerUpdated_XProxy")]		HRESULT NotifyTVETriggerUpdated_XProxy([in] ITVETrigger *pTrigger,[in]  BOOL fActive, [in] long lChangedFlags);	// changedFlags : NTRK_grfDiff
		[         id(3),  helpstring("method ReInitCurrNavState")]					HRESULT ReInitCurrNavState([in] long lReserved);
	
} ;	
// ----------------------------------------------------
//		Filter interfaces
// -----------------------------------------------------
[
    object,
	uuid (05500180-FAA5-4df9-8246-BFC23AC5CEA8),
//	dual,
	helpstring("TVEFilter Interface"),
	pointer_default(unique)
]
interface ITVEFilter : IUnknown
{
//	[propget, id(2),  helpstring("property Supervisor")]		HRESULT Supervisor([out, retval] IDispatch **ppSuperDsp);
	[propget, id(3),  helpstring("property SupervisorPunk")]	HRESULT SupervisorPunk([out, retval] IUnknown **ppSuperPunk);

	[propget, id(4),  helpstring("property IPAdapterAddress")]	HRESULT IPAdapterAddress([out, retval] BSTR *pbstrIPAddr);
	[propput, id(4), helpstring("property IPAdapterAddress")]	HRESULT IPAdapterAddress([in] BSTR bstrIPAddr);

	[propget, id(5),  helpstring("property StationID")]			HRESULT StationID([out, retval] BSTR *pbstrStationID);
	[propput, id(5),  helpstring("property StationID")]			HRESULT StationID([in] BSTR bstrStationID);

	[propget, id(6),  helpstring("property MulticastList")]			HRESULT MulticastList([out, retval] BSTR *pbstrMulticastList);
	[propget, id(7),  helpstring("property AdapterDescription")]	HRESULT AdapterDescription([out, retval] BSTR *pbstrAdapterDescription);

	[		  id(8),  helpstring("method ReTune")]				HRESULT ReTune();

							// should move to FilterHelper
	[propget, id(9),  helpstring("property HaltFlags")]			HRESULT	HaltFlags([out, retval] LONG *plGrfHaltFlags);
	[propput, id(9),  helpstring("property HaltFlags")]			HRESULT HaltFlags([in] LONG lGrfHaltFlags);

	[		  id(10), helpstring("method ParseCCBytePair")]		HRESULT	ParseCCBytePair([in] LONG lByteType, [in] BYTE byte1, [in] BYTE byte2);

	[propget, id(11), helpstring("property IPSinkAdapterAddress")]	HRESULT IPSinkAdapterAddress([out, retval] BSTR *pbstrIPAddr);

//	[propput, id(12),  helpstring("property SupervisorHelperPunk")]		HRESULT get_SupervisorHelperPunk([out] IUnknown **ppSuperHelper);

};

// ------------------------------------------------------
[
    object,
	uuid (05500280-FAA5-4df9-8246-BFC23AC5CEA8),
	hidden,
	helpstring("ITVEFilter_Helper Interface"),
	pointer_default(unique)
]
interface ITVEFilter_Helper : IUnknown
{
//	[id(1),helpstring("method ConnectParent")]		HRESULT ConnectParent([in] ITVEEnhancement *pEnhancement);
} ;


[
    object,
	uuid (05500190-FAA5-4df9-8246-BFC23AC5CEA8),
	dual,
	helpstring("ITVETriggerCtrl Interface"),
	pointer_default(unique)
]
interface ITVETriggerCtrl : IDispatch
{
	[propput, id(1)] HRESULT enabled([in] VARIANT_BOOL newVal);
	[propget, id(1)] HRESULT enabled([out, retval] VARIANT_BOOL* pVal);
	[propget, id(2)] HRESULT sourceID([out, retval] BSTR* pbstrID);
	[propput, id(3)] HRESULT releasable([in] VARIANT_BOOL newVal);
	[propget, id(3)] HRESULT releasable([out, retval] VARIANT_BOOL* pVal);
	[propget, id(4)] HRESULT backChannel([out, retval] BSTR* pVal);
	[propget, id(5)] HRESULT contentLevel([out, retval] double* pVal);
} ;

[
    object,
	uuid (05500191-FAA5-4df9-8246-BFC23AC5CEA8),
	hidden,
	helpstring("ITVETriggerCtrl_Helper Interface"),
	pointer_default(unique)
]
interface ITVETriggerCtrl_Helper : IUnknown
{
	[propput, id(1)] HRESULT sourceID([in] BSTR pSourceUUID);			// cache the SourceID
	[propget, id(2)] HRESULT TopLevelPage([out, retval] BSTR *pURL);	// return the cached URL (???) of the top web page 

} ;

// ---------------------------------------------------------------------------------------
// ***************************************************************************************
// ----------------------------------------------------------------------------------------
[
    uuid(1700C001-6666-4f66-B1E2-BF3C9FBB9BA6),		// new
	version(1.0),
	helpstring("Microsoft TVE Library and DShow Filter")
]
library MSTvELib
{
	importlib("stdole32.tlb");
	importlib("stdole2.tlb");
// -------------------------------------------------------
typedef enum NENH_grfDiff			// need to fill out..
{
	NENH_grfNone				= 0x0000,				// no flags
	NENH_grfDescription			= 0x00000001,
	NENH_grfIsPrimary			= 0x00000002,
	NENH_grfProtocolVersion		= 0x00000004,
	NENH_grfSessionUserName		= 0x00000008,
	NENH_grfSessionId			= 0x00000010,
	NENH_grfSessionVersion		= 0x00000020,
	NENH_grfSessionIPAddress	= 0x00000040,
	NENH_grfSessionName			= 0x00000080,
	NENH_grfEmailAddresses		= 0x00000100,
	NENH_grfPhoneNumbers		= 0x00000200,
	NENH_grfUUID				= 0x00000400,
	NENH_grfStartTime			= 0x00000800,
	NENH_grfStopTime			= 0x00001000,
	NENH_grfType 				= 0x00002000,
	NENH_grfTveType				= 0x00004000,
	NENH_grfTveSize				= 0x00008000,
	NENH_grfTveLevel			= 0x00010000,
	NENH_grfAttributes			= 0x00020000,
	NENH_grfRest				= 0x00040000,
	NENH_grfVariationAdded		= 0x00080000,
	NENH_grfVariationRemoved	= 0x00100000,
	NENH_grfDescriptionURI		= 0x00200000,
	NENH_grfSomeVarIP			= 0x00400000,
	NENH_grfSomeVarText			= 0x00800000,
	NENH_grfSomeVarBandwidth	= 0x01000000,
	NENH_grfSomeVarLanguages 	= 0x02000000,
	NENH_grfSomeVarAttribute	= 0x04000000,
	NENH_grfUnused				= 0x08000000,
	NENH_grfSAPVersion			= 0x10000000,
	NENH_grfSAPAddressType		= 0x20000000,
	NENH_grfSAPOther			= 0x40000000,
	NENH_grfSAPEncryptComp		= 0x80000000,
	NENH_grfAnyIP				= (NENH_grfSomeVarIP | NENH_grfVariationAdded | NENH_grfVariationRemoved),
	NENH_grfAll					= (NENH_grfSomeVarAttribute<<1) - 1				// all flags
} NENH_grfDiff;

typedef enum NVAR_grfDiff	
{	
	NVAR_grfNone				= 0x0000,
	NVAR_grfDescription			= 0x0001,
	NVAR_grfMediaName			= 0x0002,
	NVAR_grfMediaTitle			= 0x0004,
	NVAR_grfFilePort			= 0x0008,
	NVAR_grfFileIPAddress		= 0x0010,
	NVAR_grfFileIPAdapter		= 0x0020,
	NVAR_grfTriggerPort			= 0x0040,
	NVAR_grfTriggerIPAddress	= 0x0080,
	NVAR_grfTriggerIPAdapter	= 0x0100,
	NVAR_grfAttributes			= 0x0200,
	NVAR_grfLanguages			= 0x0400,
	NVAR_grfBandwidth			= 0x0800,
	NVAR_grfBandwidthInfo		= 0x1000,
	NVAR_grfRest				= 0x2000,
	NVAR_grfAnyIP				= (NVAR_grfFilePort | NVAR_grfFileIPAddress | NVAR_grfFileIPAdapter |
								  	NVAR_grfTriggerPort | NVAR_grfTriggerIPAddress | NVAR_grfTriggerIPAdapter),			
	NVAR_grfAnyText				= (NVAR_grfDescription | NVAR_grfMediaName | NVAR_grfMediaTitle),			
	NVAR_grfAnyBandwidth		= (NVAR_grfBandwidth | NVAR_grfBandwidthInfo),			
	NVAR_grfAnyAttribute		= (NVAR_grfAttributes | NVAR_grfRest),			

	NVAR_grfAll					= (NVAR_grfRest<<1) - 1				// all flags
} NVAR_grfDiff;


typedef enum NTRK_grfDiff					// attempt at a bit-field flag
{
	NTRK_grfNone	= 0x00,
	NTRK_grfURL		= 0x01,
	NTRK_grfName	= 0x02,
	NTRK_grfScript	= 0x04,
	NTRK_grfDate	= 0x08,
	NTRK_grfTVELevel= 0x10,
	NTRK_grfExpired = 0x20,					// trigger arrived out of date 
	NTRK_grfRest	= 0x40,
	NTRK_grfAll		= (NTRK_grfRest<<1) - 1				// all flags
}	NTRK_grfDiff;

typedef enum NFLT_grfHaltFlags				// flags to turn various processing bits off
{
	NFLT_grfNone	        = 0x00,

	NFLT_grfTA_Listen		= 0x01,			// don't listen for CC (XOverLink) triggers
	NFLT_grfTA_Decode	    = 0x02,			// don't accumulate byte data for XOverLink triggers into strings
	NFLT_grfTA_Parse		= 0x04,			// don't parse any XOverLink data
	
	NFLT_grfTB_AnncListen   = 0x10,			// suspend listening for announcement packets if set
	NFLT_grfTB_AnncDecode   = 0x20,			// suspend decoding and processing of announcement packets if set
	NFLT_grfTB_AnncParse	= 0x40,			// don't parse any announcements

	NFLT_grfTB_TrigListen   = 0x100,		// suspend listening for transport B triggers
	NFLT_grfTB_TrigDecode   = 0x200,		// suspend listening for transport B triggers
	NFLT_grfTB_TrigParse	= 0x400,		// don't parse any transport B triggers

	NFLT_grfTB_DataListen   = 0x1000,		// suspend listening for transport B data (files)
	NFLT_grfTB_DataDecode   = 0x2000,		// suspend listening for transport B data (files)
	NFLT_grfTB_DataParse	= 0x4000,		// don't parse any transport B data (files)

	NFLT_grf_ExpireQueue	= 0x10000,		// turn expire queue processing on and off
	NFLT_grf_Extra1			= 0x100000,		// extra flag
	NFLT_grf_Extra2			= 0x200000,		// extra flag
	NFLT_grf_Extra3			= 0x400000,		// extra flag
	NFLT_grf_Extra4			= 0x800000		//extra flag 

}	NFLT_grfHaltFlags;


// -------------------------------------------------	
// ---------------------------------------------------
//			CAUTION - if Change events below, need to perform (after MIDL compiling this file): 
//						CTVESupervisor->Implement Connection Point (_ITVEEvents)
//					AND	
//						CTVEFeature->Implement Connection Point (_ITVEEvents)
//					  to regenerate the  CProxy_ITVEEvents<> code.	
//
//					Select Browse... button, goto filters\tve\mstve\objd\i386 directory, use mstve.tlb 
//
//					Note thefile MSTvECP.h must be checked out.	
//
	
	[
		uuid(05500000-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("_ITVEEvents Interface")
	]
	dispinterface _ITVEEvents
	{
		properties:
		methods:
		[id(2101), helpstring("method NotifyTVETune")]					HRESULT NotifyTVETune([in] NTUN_Mode tuneMode, [in] ITVEService *pService, [in] BSTR bstrDescription, [in] BSTR bstrIPAdapter);
		[id(2102), helpstring("method NotifyTVEEnhancementNew")]		HRESULT NotifyTVEEnhancementNew([in] ITVEEnhancement *pEnh);
		[id(2103), helpstring("method NotifyTVEEnhancementUpdated")]	HRESULT NotifyTVEEnhancementUpdated([in] ITVEEnhancement *pEnh, [in] long lChangedFlags);	// changedFlags : NENH_grfDiff
		[id(2104), helpstring("method NotifyTVEEnhancementStarting")]	HRESULT NotifyTVEEnhancementStarting([in] ITVEEnhancement *pEnh);
		[id(2105), helpstring("method NotifyTVEEnhancementExpired")]	HRESULT NotifyTVEEnhancementExpired([in] ITVEEnhancement *pEnh);
		[id(2106), helpstring("method NotifyTVETriggerNew")]			HRESULT NotifyTVETriggerNew([in] ITVETrigger *pTrigger,[in]  BOOL fActive);
		[id(2107), helpstring("method NotifyTVETriggerUpdated")]		HRESULT NotifyTVETriggerUpdated([in] ITVETrigger *pTrigger,[in]  BOOL fActive, [in] long lChangedFlags);	// changedFlags : NTRK_grfDiff
		[id(2108), helpstring("method NotifyTVETriggerExpired")]		HRESULT NotifyTVETriggerExpired([in] ITVETrigger *pTrigger,[in]  BOOL fActive);
		[id(2109), helpstring("method NotifyTVEPackage")]				HRESULT NotifyTVEPackage([in] NPKG_Mode engPkgMode, [in] ITVEVariation *pVariation, [in] BSTR bstrUUID, [in] long  cBytesTotal, [in] long  cBytesReceived);
		[id(2110), helpstring("method NotifyTVEFile")]					HRESULT NotifyTVEFile([in] NFLE_Mode engFileMode, [in] ITVEVariation *pVariation, [in] BSTR bstrUrlName, [in] BSTR bstrFileName);
		[id(2111), helpstring("method NotifyTVEAuxInfo")]				HRESULT NotifyTVEAuxInfo([in] NWHAT_Mode engAuxInfoMode, [in] BSTR bstrAuxInfoString, [in] long lChangedFlags, [in] long lErrorLine);	// WhatIsIt is NWHAT_Mode - lChangedFlags is NENH_grfDiff or NTRK_grfDiff treated as error bits
	};

	[
		uuid(05500001-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVETrigger Class")
	]
	coclass TVETrigger
	{
		[default] interface ITVETrigger;
		interface ITVETrigger_Helper;
	};
// ----------------------------------------------------

	[
		uuid(05500002-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVETrack Class")
	]
	coclass TVETrack
	{
		[default] interface ITVETrack;
		interface ITVETrack_Helper;
	};
// ---------------------------------
//	[
//		uuid(05500012-FAA5-4df9-8246-BFC23AC5CEA8),
//		helpstring("TVETracks Class")
//	]
//	coclass TVETracks
//	{
//		[default] interface ITVETracks;
//	};
// ----------------------------------------------------

	[
		uuid(05500003-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEVariation Class")
	]
	coclass TVEVariation
	{
		[default] interface ITVEVariation;
		interface ITVEVariation_Helper;
	};
//	[
//		uuid(05500013-FAA5-4df9-8246-BFC23AC5CEA8),
//		helpstring("TVEVariations Class")
//	]
//	coclass TVEVariations
//	{
//		[default] interface ITVEVariations;
//	};
// ---------------------------------------------------
	[
		uuid(05500004-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEEnhancement Class")
	]
	coclass TVEEnhancement
	{
		[default] interface ITVEEnhancement;
		interface ITVEEnhancement_Helper;
	};

			// --------------------------------

	[
		uuid(05500014-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEEnhancements Class")
	]
	coclass TVEEnhancements
	{
		[default] interface ITVEEnhancements;
	};
// ---------------------------------------------------


	[
		uuid(05500005-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEService Class")
	]
	coclass TVEService
	{
		[default] interface ITVEService;
		interface ITVEService_Helper;
	};

		[
		uuid(05500025-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEFeature Class")
	]
	coclass TVEFeature
	{
		[default] interface ITVEFeature;
		[default, source] dispinterface _ITVEEvents;		// out going event handler 
		interface _ITVEEvents;								// incoming event interface
//		interface ITVEService_Helper;
	};

		// -------------------------------

	[
		uuid(05500015-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEServices Class")
	]
	coclass TVEServices
	{
		[default] interface ITVEServices;
	};


// ---------------------------------------------------
	[
		uuid(05500006-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVESupervisor Class")
	]
	coclass TVESupervisor
	{
		[default] interface ITVESupervisor;
		[default, source] dispinterface _ITVEEvents;
		interface ITVESupervisor_Helper;
	};
/*
	[
		hidden,
		uuid(05500406-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("ITVESupervisorGITProxy Class")
	]
	coclass TVESupervisorGITProxy
	{
		[default] interface ITVESupervisorGITProxy;
	}; 
	*/
// ---------------------------------------------------

	[
		uuid(05500021-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEAttrMap Class")
	]	
	coclass TVEAttrMap
	{
		[default] interface ITVEAttrMap;
	};

	[
		uuid(05500022-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEAttrTimeQ Class")
	]	
	coclass TVEAttrTimeQ
	{
		[default] interface ITVEAttrTimeQ;
	};

	[
		uuid(05500030-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEMCast Class")
	]	
	coclass TVEMCast
	{
		[default] interface ITVEMCast;
	};

	[
		uuid(05500031-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEMCasts Class")
	]
	coclass TVEMCasts
	{
		[default] interface ITVEMCasts;
	};

	[
		uuid(05500032-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEMCastManager Class")
	]
	coclass TVEMCastManager
	{
		[default] interface ITVEMCastManager;
		interface ITVEMCastManager_Helper;
	};


	[
		uuid(05500040-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEMCastCallback Class")
	]
	coclass TVEMCastCallback
	{
		[default] interface ITVEMCastCallback;
	};
	[
		uuid(05500041-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVECBAnnc Class")
	]
				// specific Multicast Callbacks
	coclass TVECBAnnc
	{
		[default] interface ITVECBAnnc;
	};
	[
		uuid(05500042-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVECBTrig Class")
	]
	coclass TVECBTrig
	{
		[default] interface ITVECBTrig;
	};
	[
		uuid(05500043-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVECBFile Class")
	]
	coclass TVECBFile
	{
		[default] interface ITVECBFile;
	};
	[
		uuid(05500049-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVECBDummy Class")
	]
	coclass TVECBDummy
	{
		[default] interface ITVECBDummy;
	};


	[
		uuid(05500050-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEFile Class")
	]
	coclass TVEFile
	{
		[default] interface ITVEFile;
	}

				// This control gets CoCreate'ed by an app to handle hooking things together
	[
		uuid(05500070-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVENavAid Class")
	]
	coclass TVENavAid
	{
		[default] interface ITVENavAid;						// controllable from HTML
		interface ITVENavAid_Helper;						// internal control methods
		interface ITVENavAid_NoVidCtl;						// to work without the VidControl
	};

				// this is the DShow TVE Receiver Filter that encapsulates a TVESupervisor 
				//  object in a in a DShow graph
	[
		uuid(05500080-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVEFilter Class")
	]
	coclass TVEFilter
	{
//		[default, source] dispinterface _ITVEEvents;
		[default] interface ITVEFilter;
		interface ITVEFilter_Helper;
	};

				// This control gets CoCreated by HTML in <OBJECT TYPE="application\tve-trigger">
				//  Implements the tve-trigger receiver object properties as per Atvef Spec
	[
		uuid(05500091-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("_ITVETriggerCtrlEvents Interface")
	]
	dispinterface _ITVETriggerCtrlEvents 
	{
		properties:
		methods:
	};


	[
		uuid(05500090-FAA5-4df9-8246-BFC23AC5CEA8),
		helpstring("TVETriggerCtrl Class")
	]
	coclass TVETriggerCtrl
	{
		[default] interface ITVETriggerCtrl;					// controllable from HTML
		interface ITVETriggerCtrl_Helper;						// internal control methods
		[default, source] dispinterface _ITveTriggerCtrlEvents;	// fire control events to HTML
//		interface _ITVEvents;									// respond to slew of TVE events
	};

};
