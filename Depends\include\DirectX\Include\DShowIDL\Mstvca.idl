// MSTvCA.idl : IDL source for MSTvCA.dll
//

// This file will be processed by the MIDL tool to
// produce the type library (CA.tlb) and marshalling code.

import "oaidl.idl";
import "ocidl.idl";
#include "olectl.h"

/*	[
		object,
		dual,
		uuid(49a32d3c-7d85-11d2-8895-00c04f794967),
		helpstring("ITuneRequest Interface"),
		pointer_default(unique)
	]
	interface ITuneRequest : IDispatch
	{
	};
*/
import "tuner.idl";			// includes the whole BDA world!  Yecko!

	interface ICAManager;
	interface ICARequest;
	interface ICAPolicy;
	interface ICAPolicies;
	interface ICAPoliciesInternal;
	interface ICAToll;
	interface ICATolls;
	interface ICATollsInternal;
	interface ICADenial;
	interface ICADenials;
	interface ICAOffer;
	interface ICAOffers;
	interface ICAComponent;
	interface ICAComponents;

#define COLLECTID_Item				0
#define COLLECTID_Count				1
#define COLLECTID_Add				2
#define COLLECTID_Remove			3
#define COLLECTID_AddNew			4
#define COLLECTID_CountDenied		5		// ICADenials
#define COLLECTID_CountSelected		6		// ICADenials
#define COLLECTID_PaySelectedTolls	7		// ICADenials

typedef enum 
{
	Unselected		= 0,
	Selected		= 1
} CATollState;

typedef enum 
{
	Denied			 = 0,
	Transient		 = 1,
//	Allowed			 = 2,
	DescriptionShort = 10,			// one of the description fields changed
	DescriptionLong  = 11,			//    via a ICADenial::put_Description() 
	DescriptionHTML  = 12,			// 
	DescriptionXML   = 13			//    (keep these current+10 with list below...)
} CADenialState;

typedef enum 	
{
	Short		= 0,		// must be numeric, starting at 0.  (Used as array index).
	Long		= 1,		//  in approximatly order of length in bytes
	URL			= 2,
	HTML		= 3,
	XML			= 4,
	kDescEnd	= 5			// invalid format, 1+ last real one (Used as array length).
} CADescFormat;


typedef enum 
{
	Request  = 1,
	ComponentX = 2,
	Offers   = 4,
	PaidTolls = 8,
	Policies  = 16,
	Standard  = 15,		// all put the policies
	All = 31			// all
} CAUIDisplayFields;

			// ---------------------------------------------------------------
			//   if add methods to ICAManagerInternal, be sure to add MAGICCALL in CAManagerProxy too..
			// ---------------------------------------------------------------

	[
		object,
		uuid(11166301-DF8A-463a-B620-7BEC23542010),
		dual,											// Helpers are Dual, IUnknown... so see from VB
		hidden, restricted, 
		helpstring("ICAManagerInternal Interface"),
		pointer_default(unique)
	]
	interface ICAManagerInternal : IUnknown
	{
		[		  id(1),   helpstring("method Save")]						HRESULT Save();
		[		  id(2),   helpstring("method Load")]						HRESULT Load();
		[propput, id(3),   helpstring("property MarkDirty")]				HRESULT MarkDirty([in] BOOL fDirty);
		[propget, id(3),   helpstring("property MarkDirty")]				HRESULT MarkDirty([out, retval] BOOL *pfDirty);
		[propput, id(4),   helpstring("property TuneRequest")]				HRESULT TuneRequest([in] ITuneRequest *ptunereq);
		[		  id(5),   helpstring("method GetDefaultUI")]				HRESULT GetDefaultUI([out] HWND *phwnd);
		[		  id(6),   helpstring("method SetDefaultUI")]				HRESULT SetDefaultUI([in] HWND hwnd);
					// return the main CAManager object, not the proxy one.
		[propget, id(7),   helpstring("property CAManagerMain")]			HRESULT CAManagerMain([out,retval] ICAManager **ppManagerMain);
		[propput, id(8),   helpstring("property BroadcastEventService")]	HRESULT BroadcastEventService([in] IBroadcastEvent *pBroadcastEventService);
		[propget, id(8),   helpstring("property BroadcastEventService")]	HRESULT BroadcastEventService([out, retval] IBroadcastEvent **ppBroadcastEventService);
        [         id(9),   helpstring("method DisplayDefaultUI")]           HRESULT DisplayDefaultUI([in] VARIANT_BOOL fDisplay);
        [         id(10),  helpstring("method EnableDefaultUIPayTollsButton")]  HRESULT EnableDefaultUIPayTollsButton([in] VARIANT_BOOL fEnabled);
        [         id(11),  helpstring("method UpdateDefaultUIForToll")]     HRESULT UpdateDefaultUIForToll([in] ICAToll *pToll,[in] CATollState enState);
		[propput, id(12),  helpstring("property TuneRequestInt")]			HRESULT TuneRequestInt([in] ITuneRequest *ptunereq);
        [         id(13),  helpstring("method AddDenialsFor")]				HRESULT AddDenialsFor([in] IUnknown *pUnk);     // see also ICAManager::get_DenialsFor()
        [         id(14),  helpstring("method RemoveDenialsFor")]           HRESULT RemoveDenialsFor([in] IUnknown *pUnk);

		[		  id(2201),helpstring("method NotifyRequestActivated")]		HRESULT NotifyRequestActivated([in] ICARequest *pReq);
		[		  id(2202),helpstring("method NotifyRequestDeactivated")]	HRESULT NotifyRequestDeactivated([in] ICARequest *pReq);
		[		  id(2203),helpstring("method NotifyOfferAdded")]			HRESULT NotifyOfferAdded([in] ICAOffer *pOffer,[in] long cOffers);
		[		  id(2204),helpstring("method NotifyOfferRemoved")]			HRESULT NotifyOfferRemoved([in] ICAOffer *pOffer,[in] long cOffers);
		[		  id(2205),helpstring("method NotifyPolicyAdded")]			HRESULT NotifyPolicyAdded([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[		  id(2206),helpstring("method NotifyPolicyRemoved")]		HRESULT NotifyPolicyRemoved([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[		  id(2207),helpstring("method NotifyRequestDenialAdded")]	HRESULT NotifyRequestDenialAdded([in] ICARequest *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2208),helpstring("method NotifyRequestDenialRemoved")]	HRESULT NotifyRequestDenialRemoved([in] ICARequest *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2209),helpstring("method NotifyDenialTollAdded")]		HRESULT NotifyDenialTollAdded([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2210),helpstring("method NotifyDenialTollRemoved")]	HRESULT NotifyDenialTollRemoved([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2211),helpstring("method NotifyTollDenialAdded")]		HRESULT NotifyTollDenialAdded([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2212),helpstring("method NotifyTollDenialRemoved")]	HRESULT NotifyTollDenialRemoved([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2213),helpstring("method NotifyOfferTollAdded")]		HRESULT NotifyOfferTollAdded([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2214),helpstring("method NotifyOfferTollRemoved")]		HRESULT NotifyOfferTollRemoved([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[         id(2215),helpstring("method NotifyTollStateChanged")]		HRESULT NotifyTollStateChanged([in] ICAToll *pToll, [in] CATollState enStateLast);
		[         id(2216),helpstring("method NotifyDenialStateChanged")]	HRESULT NotifyDenialStateChanged([in] ICADenial *pDenial, [in] CADenialState enStateLast);
		[         id(2217),helpstring("method NotifyComponentDenialAdded")]		HRESULT NotifyComponentDenialAdded([in] ICAComponent *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[         id(2218),helpstring("method NotifyComponentDenialRemoved")]	HRESULT NotifyComponentDenialRemoved([in] ICAComponent *pReq, [in] ICADenial *pDenial,[in] long cDenials);

	};

	[
		object,
		uuid(11166302-DF8A-463a-B620-7BEC23542010),
		dual,											// Helpers are Dual, IUnknown... so see from VB
		hidden, restricted, 
		helpstring("ICAManagerXProxy Interface"),
		pointer_default(unique)
	]
	interface ICAManagerXProxy : IUnknown
	{
        [propget, id(1),   helpstring("property PunkCAManagerProxy")]                   HRESULT PunkCAManagerProxy([out,retval] IUnknown **ppUnkCAManagerProxy);
		[		  id(2201),helpstring("method NotifyRequestActivated_XProxy")]          HRESULT NotifyRequestActivated_XProxy([in] ICARequest *pReq);
		[		  id(2202),helpstring("method NotifyRequestDeactivated_XProxy")]        HRESULT NotifyRequestDeactivated_XProxy([in] ICARequest *pReq);
		[		  id(2203),helpstring("method NotifyOfferAdded_XProxy")]                HRESULT NotifyOfferAdded_XProxy([in] ICAOffer *pOffer,[in] long cOffers);
		[		  id(2204),helpstring("method NotifyOfferRemoved_XProxy")]              HRESULT NotifyOfferRemoved_XProxy([in] ICAOffer *pOffer,[in] long cOffers);
		[		  id(2205),helpstring("method NotifyPolicyAdded_XProxy")]               HRESULT NotifyPolicyAdded_XProxy([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[		  id(2206),helpstring("method NotifyPolicyRemoved_XProxy")]             HRESULT NotifyPolicyRemoved_XProxy([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[		  id(2207),helpstring("method NotifyRequestDenialAdded_XProxy")]        HRESULT NotifyRequestDenialAdded_XProxy([in] ICARequest *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2208),helpstring("method NotifyRequestDenialRemoved_XProxy")]      HRESULT NotifyRequestDenialRemoved_XProxy([in] ICARequest *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2209),helpstring("method NotifyDenialTollAdded_XProxy")]           HRESULT NotifyDenialTollAdded_XProxy([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2210),helpstring("method NotifyDenialTollRemoved_XProxy")]         HRESULT NotifyDenialTollRemoved_XProxy([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2211),helpstring("method NotifyTollDenialAdded_XProxy")]           HRESULT NotifyTollDenialAdded_XProxy([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2212),helpstring("method NotifyTollDenialRemoved_XProxy")]         HRESULT NotifyTollDenialRemoved_XProxy([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2213),helpstring("method NotifyOfferTollAdded_XProxy")]            HRESULT NotifyOfferTollAdded_XProxy([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2214),helpstring("method NotifyOfferTollRemoved_XProxy")]          HRESULT NotifyOfferTollRemoved_XProxy([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[         id(2215),helpstring("method NotifyTollStateChanged_XProxy")]          HRESULT NotifyTollStateChanged_XProxy([in] ICAToll *pToll, [in] CATollState enStateLast);
		[         id(2216),helpstring("method NotifyDenialStateChanged_XProxy")]        HRESULT NotifyDenialStateChanged_XProxy([in] ICADenial *pDenial, [in] CADenialState enStateLast);
		[         id(2217),helpstring("method NotifyComponentDenialAdded_XProxy")]      HRESULT NotifyComponentDenialAdded_XProxy([in] ICAComponent *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[         id(2218),helpstring("method NotifyComponentDenialRemoved_XProxy")]    HRESULT NotifyComponentDenialRemoved_XProxy([in] ICAComponent *pReq, [in] ICADenial *pDenial,[in] long cDenials);
	};

			// ---------------------------------------------------------------


			// ---------------------------------------------------------------

	[
		object,
		uuid(11166420-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAPolicies Interface"),
		pointer_default(unique)
	]
	interface ICAPolicies : IDispatch
	{
        [propget, id(DISPID_NEWENUM), restricted] HRESULT _NewEnum([out, retval] IUnknown **ppCollection);
        [propget, id(COLLECTID_Count)]			  HRESULT Count([out, retval] long *Count);
        [propget, id(COLLECTID_Item)]			  HRESULT Item([in] VARIANT Index, [out, retval] ICAPolicy **ppPolicy);
        [         id(COLLECTID_Add)]			  HRESULT Add([in] ICAPolicy *pPolicy);
        [         id(COLLECTID_Remove)]			  HRESULT Remove([in] VARIANT Index);
	};

	[	object,
		uuid(11166421-DF8A-463a-B620-7BEC23542010),
		dual,
		hidden, restricted, 
		helpstring("ICAPoliciesInternal Interface"),
		pointer_default(unique)
	]
	interface ICAPoliciesInternal : IUnknown
	{
		 [id(1),helpstring("method SetCAManager")]	HRESULT SetCAManager([in] ICAManager *pManager);
		 [id(2),helpstring("method CheckRequest")]	HRESULT CheckRequest([in] ICARequest *pReq);
	};

			// ---------------------------------------------------------------

	[
		object,
		uuid(11166430-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICATolls Interface"),
		pointer_default(unique)
	]
	interface ICATolls : IDispatch
	{
        [propget, id(DISPID_NEWENUM), restricted]	HRESULT _NewEnum([out, retval] IUnknown **ppCollection);
        [propget, id(COLLECTID_Count)]				HRESULT Count([out, retval] long *Count);
        [propget, id(COLLECTID_Item)]				HRESULT Item([in] VARIANT Index, [out, retval] ICAToll **ppToll);
        [	      id(COLLECTID_Add)]				HRESULT Add([in] ICAToll *pToll);
        [	      id(COLLECTID_Remove)]				HRESULT Remove([in] VARIANT Index);
	};

	[
		object,
		uuid(11166431-DF8A-463a-B620-7BEC23542010),
		dual,
		hidden, restricted, 
		helpstring("ICATolls Internal Interface"),
		pointer_default(unique)
	]
	interface ICATollsInternal : IUnknown			// workaround for not being able to get _ICAResDenialEvents to work
	{				
		 [		  id(1),helpstring("method SetCAManager")]					HRESULT SetCAManager([in] ICAManager *pManager);
		 [		  id(2),helpstring("method GetCAManager")]					HRESULT GetCAManager([out] ICAManager **ppManager);
 		 [		  id(3),helpstring("method SetMustPersist")]				HRESULT SetMustPersist([in] BOOL fMustPersist);
 		 [		  id(4),helpstring("method Save")]							HRESULT Save([in] IStorage *pstore,[in] BSTR bstrPrefix);
		 [		  id(5),helpstring("method Load")]							HRESULT Load([in] IStorage *pstore,[in] BSTR bstrPrefix);
         [	      id(6),helpstring("method NotifyStateChanged")]			HRESULT NotifyStateChanged([in] ICAToll *pToll, [in] CATollState enStateFrom);
         [	      id(7),helpstring("method NotifyTollSelectionChanged")]	HRESULT NotifyTollSelectionChanged([in] ICAToll *pToll, [in] BOOL fSelected);
  	};
			// ---------------------------------------------------------------


	[
		object,
		uuid(11166440-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICADenials Interface"),
		pointer_default(unique)
	]
	interface ICADenials : IDispatch
	{
        [propget, id(DISPID_NEWENUM), restricted]						HRESULT _NewEnum([out, retval] IUnknown **ppCollection);
        [propget, id(COLLECTID_Count) ]									HRESULT Count([out, retval] long *Count);
        [propget, id(COLLECTID_Item)]									HRESULT Item([in] VARIANT Index, [out, retval] ICADenial **ppDenial);
		[propget, id(COLLECTID_AddNew), helpstring("property AddNew")]	HRESULT AddNew([in] ICAPolicy *ppolicy, [in] BSTR bstrShortDesc, [in] IUnknown *pUnkDeniedObject, [in] long enDenialState, [out, retval] ICADenial **ppDenial);
        [         id(COLLECTID_Remove)]									HRESULT Remove([in] VARIANT Index);
        [propget, id(COLLECTID_CountDenied)]							HRESULT CountDenied([out, retval] long *Count);
        [propget, id(COLLECTID_CountSelected)]							HRESULT CountSelected([out, retval] long *Count);
        [		  id(COLLECTID_PaySelectedTolls)]						HRESULT PaySelectedTolls();
	};

	[
		object,
		uuid(11166441-DF8A-463a-B620-7BEC23542010),
		dual,
		hidden, restricted, 
		helpstring("ICADenialsInternal Interface"),
		pointer_default(unique)
	]
	interface ICADenialsInternal : IUnknown
	{
		[  id(1),helpstring("method SetCAManager")]				HRESULT SetCAManager([in] ICAManager *pManager);		
		[  id(2),helpstring("method NotifyDenialStateChanged")]	HRESULT NotifyDenialStateChanged([in] ICADenial *pDenial, [in] CADenialState enStateLast);
 	};


			// ---------------------------------------------------------------

	[
		object,
		uuid(11166450-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAOffers Interface"),
		pointer_default(unique)
	]
	interface ICAOffers : IDispatch
	{
        [propget, id(DISPID_NEWENUM) , restricted]						HRESULT _NewEnum([out, retval] IUnknown **ppCollection);
        [propget, id(COLLECTID_Count)]									HRESULT Count([out, retval] long *Count);
        [propget, id(COLLECTID_Item)]									HRESULT Item([in] VARIANT Index, [out, retval] ICAOffer **ppOffer);
        [propget, id(COLLECTID_AddNew), helpstring("property AddNew")]	HRESULT AddNew([in] ICAPolicy *pPolicy, [in] BSTR bstrName, [in] DATE dateStart, [in] DATE dateEnd, [out, retval] ICAOffer **ppOffer);
        [		  id(COLLECTID_Remove)]									HRESULT Remove([in] VARIANT Index);
	};

	[
		object,
		uuid(11166470-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAComponents Interface"),
		pointer_default(unique)
	]
	interface ICAComponents : IDispatch
	{
        [propget, id(DISPID_NEWENUM) , restricted]						HRESULT _NewEnum([out, retval] IUnknown **ppCollection);
        [propget, id(COLLECTID_Count)]									HRESULT Count([out, retval] long *Count);
        [propget, id(COLLECTID_Item)]									HRESULT Item([in] VARIANT Index, [out, retval] ICAComponent **ppComponent);
  //      [	      id(COLLECTID_Add)]									HRESULT Add([in] ICAComponent *pComponent);
  //      [		  id(COLLECTID_Remove)]									HRESULT Remove([in] VARIANT Index);
	};


	[
		object,
		uuid(11166361-DF8A-463a-B620-7BEC23542010),
		dual,
		hidden,
		helpstring("ICAComponentInternal Interface"),
		pointer_default(unique)
	]
	interface ICAComponentInternal : IUnknown
	{
		[		  id(1), helpstring("method RemoveAllDenials")]	HRESULT RemoveAllDenials();
		[propget, id(2), helpstring("property Description")]	HRESULT Description([in] CADescFormat enFormat, [out, retval] BSTR *pbstrDescription);
	};
			// ---------------------------------------------------------------


  [
          object,
          uuid(860A3FE2-DED1-40E2-896C-057681A8A1A8),
          dual,
          helpstring("ICADefaultDlg Interface"),
          pointer_default(unique)
  ]
  interface ICADefaultDlg : IDispatch
  {
          [propput, id(DISPID_AUTOSIZE)]
          HRESULT AutoSize([in]VARIANT_BOOL vbool);
          [propget, id(DISPID_AUTOSIZE)]
          HRESULT AutoSize([out,retval]VARIANT_BOOL* pbool);
          [propput, id(DISPID_BACKCOLOR)]
          HRESULT BackColor([in]OLE_COLOR clr);
          [propget, id(DISPID_BACKCOLOR)]
          HRESULT BackColor([out,retval]OLE_COLOR* pclr);
          [propput, id(DISPID_BACKSTYLE)]
          HRESULT BackStyle([in]long style);
          [propget, id(DISPID_BACKSTYLE)]
          HRESULT BackStyle([out,retval]long* pstyle);
          [propput, id(DISPID_BORDERCOLOR)]
          HRESULT BorderColor([in]OLE_COLOR clr);
          [propget, id(DISPID_BORDERCOLOR)]
          HRESULT BorderColor([out, retval]OLE_COLOR* pclr);
          [propput, id(DISPID_BORDERSTYLE)]
          HRESULT BorderStyle([in]long style);
          [propget, id(DISPID_BORDERSTYLE)]
          HRESULT BorderStyle([out, retval]long* pstyle);
          [propput, id(DISPID_BORDERWIDTH)]
          HRESULT BorderWidth([in]long width);
          [propget, id(DISPID_BORDERWIDTH)]
          HRESULT BorderWidth([out, retval]long* width);
          [propput, id(DISPID_DRAWMODE)]
          HRESULT DrawMode([in]long mode);
          [propget, id(DISPID_DRAWMODE)]
          HRESULT DrawMode([out, retval]long* pmode);
          [propput, id(DISPID_DRAWSTYLE)]
          HRESULT DrawStyle([in]long style);
          [propget, id(DISPID_DRAWSTYLE)]
          HRESULT DrawStyle([out, retval]long* pstyle);
          [propput, id(DISPID_DRAWWIDTH)]
          HRESULT DrawWidth([in]long width);
          [propget, id(DISPID_DRAWWIDTH)]
          HRESULT DrawWidth([out, retval]long* pwidth);
          [propput, id(DISPID_FILLCOLOR)]
          HRESULT FillColor([in]OLE_COLOR clr);
          [propget, id(DISPID_FILLCOLOR)]
          HRESULT FillColor([out, retval]OLE_COLOR* pclr);
          [propput, id(DISPID_FILLSTYLE)]
          HRESULT FillStyle([in]long style);
          [propget, id(DISPID_FILLSTYLE)]
          HRESULT FillStyle([out, retval]long* pstyle);
          [propputref, id(DISPID_FONT)]
          HRESULT Font([in]IFontDisp* pFont);
          [propput, id(DISPID_FONT)]
          HRESULT Font([in]IFontDisp* pFont);
          [propget, id(DISPID_FONT)]
          HRESULT Font([out, retval]IFontDisp** ppFont);
          [propput, id(DISPID_FORECOLOR)]
          HRESULT ForeColor([in]OLE_COLOR clr);
          [propget, id(DISPID_FORECOLOR)]
          HRESULT ForeColor([out,retval]OLE_COLOR* pclr);
          [propput, id(DISPID_ENABLED)]
          HRESULT Enabled([in]VARIANT_BOOL vbool);
          [propget, id(DISPID_ENABLED)]
          HRESULT Enabled([out,retval]VARIANT_BOOL* pbool);
          [propget, id(DISPID_HWND)]
          HRESULT Window([out, retval]LONG_PTR* phwnd);			// was long* via the wizard, but doesn't compile Win64
          [propput, id(DISPID_TABSTOP)]
          HRESULT TabStop([in]VARIANT_BOOL vbool);
          [propget, id(DISPID_TABSTOP)]
          HRESULT TabStop([out, retval]VARIANT_BOOL* pbool);
          [propput, id(DISPID_TEXT)]
          HRESULT Text([in]BSTR strText);
          [propget, id(DISPID_TEXT)]
          HRESULT Text([out, retval]BSTR* pstrText);
          [propput, id(DISPID_CAPTION)]
          HRESULT Caption([in]BSTR strCaption);
          [propget, id(DISPID_CAPTION)]
          HRESULT Caption([out,retval]BSTR* pstrCaption);
          [propput, id(DISPID_BORDERVISIBLE)]
          HRESULT BorderVisible([in]VARIANT_BOOL vbool);
          [propget, id(DISPID_BORDERVISIBLE)]
          HRESULT BorderVisible([out, retval]VARIANT_BOOL* pbool);
          [propput, id(DISPID_APPEARANCE)]
          HRESULT Appearance([in]short appearance);
          [propget, id(DISPID_APPEARANCE)]
          HRESULT Appearance([out, retval]short* pappearance);
          [propput, id(DISPID_MOUSEPOINTER)]
          HRESULT MousePointer([in]long pointer);
          [propget, id(DISPID_MOUSEPOINTER)]
          HRESULT MousePointer([out, retval]long* ppointer);
          [propputref, id(DISPID_MOUSEICON)]
          HRESULT MouseIcon([in]IPictureDisp* pMouseIcon);
          [propput, id(DISPID_MOUSEICON)]
          HRESULT MouseIcon([in]IPictureDisp* pMouseIcon);
          [propget, id(DISPID_MOUSEICON)]
          HRESULT MouseIcon([out, retval]IPictureDisp** ppMouseIcon);
          [propputref, id(DISPID_PICTURE)]
          HRESULT Picture([in]IPictureDisp* pPicture);
          [propput, id(DISPID_PICTURE)]
          HRESULT Picture([in]IPictureDisp* pPicture);
          [propget, id(DISPID_PICTURE)]
          HRESULT Picture([out, retval]IPictureDisp** ppPicture);
          [propput, id(DISPID_VALID)]
          HRESULT Valid([in]VARIANT_BOOL vbool);
          [propget, id(DISPID_VALID)]
          HRESULT Valid([out, retval]VARIANT_BOOL* pbool);
  };
			// ---------------------------------------------------------------

[
	uuid(11166000-DF8A-463a-B620-7BEC23542010),
	version(1.0),
	helpstring("Microsoft TV CA Type Library")
]
library MSTvCALib
{
	importlib("stdole32.tlb");
	importlib("stdole2.tlb");

// ---------------------------------------------------
//			CAUTION - if Change events below, need to perform:
//					1) compile the MIDL file to generate the typelib
//					2) In VCC class view, <xxx>->Implement Connection Point (_ICA<xxx>Events)
//					      to regenerate the  CProxy_ICA<xxx>Events<> code.	
//						  Need to browse to objd\i386 directory and select MSTvCA.tlb
//					(The file MSTvCACP.h must be checked out.)	
//					Where <xxx> is Denials, Manager, Offers, Policies, Request, and Tolls

	[
		uuid(11166298-DF8A-463A-B620-7BEC23542010),
		helpstring("_ICAResDenialTreeEvents Interface")
	]
	dispinterface _ICAResDenialTreeEvents
	{
		properties:
		methods:
		[id(1),helpstring("method PaidTollSelected")]			HRESULT PaidTollSelected([in] ICAToll *pToll, [in] long fSelected);
	};


	[
		uuid(11166200-DF8A-463A-B620-7BEC23542010),
		helpstring("ICAManagerEvents Interface")
	]
	dispinterface _ICAManagerEvents
	{
		properties:
		methods:
		[id(2201),helpstring("method RequestActivated")]		HRESULT RequestActivated([in] ICARequest *pReq);
		[id(2202),helpstring("method RequestDeactivated")]		HRESULT RequestDeactivated([in] ICARequest *pReq);
		[id(2203),helpstring("method OfferAdded")]				HRESULT OfferAdded([in] ICAOffer *pOffer,[in] long cOffers);
		[id(2204),helpstring("method OfferRemoved")]			HRESULT OfferRemoved([in] ICAOffer *pOffer,[in] long cOffers);
		[id(2205),helpstring("method PolicyAdded")]				HRESULT PolicyAdded([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[id(2206),helpstring("method PolicyRemoved")]			HRESULT PolicyRemoved([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[id(2207),helpstring("method RequestDenialAdded")]		HRESULT RequestDenialAdded([in] ICARequest *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[id(2208),helpstring("method RequestDenialRemoved")]	HRESULT RequestDenialRemoved([in] ICARequest *pReq, [in] ICADenial *pDenial,[in] long cDenials);
		[id(2209),helpstring("method DenialTollAdded")]			HRESULT DenialTollAdded([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[id(2210),helpstring("method DenialTollRemoved")]		HRESULT DenialTollRemoved([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[id(2211),helpstring("method TollDenialAdded")]			HRESULT TollDenialAdded([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[id(2212),helpstring("method TollDenialRemoved")]		HRESULT TollDenialRemoved([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[id(2213),helpstring("method OfferTollAdded")]			HRESULT OfferTollAdded([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[id(2214),helpstring("method OfferTollRemoved")]		HRESULT OfferTollRemoved([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[id(2215),helpstring("method TollStateChanged")]		HRESULT TollStateChanged([in] ICAToll *pToll, [in] CATollState enState);
		[id(2216),helpstring("method DenialStateChanged")]		HRESULT DenialStateChanged([in] ICADenial *pDenial, [in] CADenialState enState);
		[id(2217),helpstring("method ComponentDenialAdded")]	HRESULT ComponentDenialAdded([in] ICAComponent *pComp, [in] ICADenial *pDenial,[in] long cDenials);
		[id(2218),helpstring("method ComponentDenialRemoved")]	HRESULT ComponentDenialRemoved([in] ICAComponent *pComp, [in] ICADenial *pDenial,[in] long cDenials);
	};

	[
		uuid(11166210-DF8A-463A-B620-7BEC23542010),
		helpstring("ICARequestEvents Interface")
	]
	dispinterface _ICARequestEvents
	{
		properties:
		methods:
		[id(1), helpstring("method CheckStarted")]	HRESULT CheckStarted([in] ICARequest *pRequest);
		[id(2), helpstring("method CheckComplete")] HRESULT CheckComplete([in] ICARequest *pRequest,[in] long cDenials);
	};
	[
		uuid(11166220-DF8A-463A-B620-7BEC23542010),
		helpstring("_ICAPoliciesEvents Interface")
	]
	dispinterface _ICAPoliciesEvents
	{
		properties:
		methods:
		[id(1), helpstring("method ItemAdded")]	  HRESULT ItemAdded([in] ICAPolicy *pPolicy, long cPolicies);
		[id(2), helpstring("method ItemRemoved")] HRESULT ItemRemoved([in] ICAPolicy *pPolicy, long cPolicies);
	};

	[
		uuid(11166230-DF8A-463A-B620-7BEC23542010),
		helpstring("ICATollsEvents Interface")
	]
	dispinterface _ICATollsEvents
	{
		properties:
		methods:
		[id(1), helpstring("method ItemAdded")]		HRESULT ItemAdded([in] ICAToll *pToll, [in] long cTolls);
		[id(2), helpstring("method ItemRemoved")]	HRESULT ItemRemoved([in] ICAToll *pToll, [in] long cTolls);
		[id(3), helpstring("method StateChanged")]	HRESULT StateChanged([in] ICAToll *pToll, [in] CATollState enStateFrom);
	};


	[
		uuid(11166240-DF8A-463A-B620-7BEC23542010),
		helpstring("ICADenialsEvents Interface")
	]
	dispinterface _ICADenialsEvents
	{
		properties:
		methods:
		[id(1), helpstring("method ItemAdded")]		HRESULT ItemAdded([in] ICADenial *pDenial, [in] long cDenials);
		[id(2), helpstring("method ItemRemoved")]	HRESULT ItemRemoved([in] ICADenial *pDenial, [in] long cDenials);
		[id(3), helpstring("method StateChanged")]	HRESULT StateChanged([in] ICADenial *pDenial, [in] CADenialState enStateFrom);
	};

	[
		uuid(11166250-DF8A-463A-B620-7BEC23542010),
		helpstring("ICAOffersEvents Interface")
	]
	dispinterface _ICAOffersEvents
	{
		properties:
		methods:
		[id(1), helpstring("method ItemAdded")]		HRESULT ItemAdded([in] ICAOffer *pOffer, [in] long cOffers);
		[id(2), helpstring("method ItemRemoved")]	HRESULT ItemRemoved([in] ICAOffer *pOffer, [in] long cOffers);
	};

	[
		uuid(11166260-DF8A-463A-B620-7BEC23542010),
		helpstring("_ICAComponentsEvents Interface")
	]
	dispinterface _ICAComponentsEvents
	{
		properties:
		methods:
		[id(1), helpstring("method ItemAdded")]		HRESULT ItemAdded([in] ICAComponent *pComponent, [in] long cComponent);
		[id(2), helpstring("method ItemRemoved")]	HRESULT ItemRemoved([in] ICAComponent *pComponent, [in] long cComponent);
	};		// --------------------------------
	[
		object,
		dual,
		uuid(11166300-DF8A-463a-B620-7BEC23542010),
		helpstring("ICAManager Interface"),
		pointer_default(unique)
	]
	interface ICAManager : IDispatch
	{
		[propget, id(1),   helpstring("property Policies")]			HRESULT Policies([out, retval] ICAPolicies **ppPolicies);
		[propget, id(2),   helpstring("property ActiveRequest")]	HRESULT ActiveRequest([out, retval] ICARequest **ppRequest);
		[propget, id(3),   helpstring("property Offers")]			HRESULT Offers([out, retval] ICAOffers **ppOffers);
		[propget, id(4),   helpstring("property PaidTolls")]		HRESULT PaidTolls([out, retval] ICATolls **ppTolls);
		[propput, id(5),   helpstring("property UseDefaultUI")]		HRESULT UseDefaultUI([in] long fUseDefaultUI);
		[propget, id(5),   helpstring("property UseDefaultUI")]		HRESULT UseDefaultUI([out, retval] long *pfUseDefaultUI);
		[propget, id(6),   helpstring("property DenialsFor")]		HRESULT DenialsFor([in] IUnknown *pUnk, [out, retval] ICADenials **ppDenials);
	};
	
	[
		object,
		uuid(11166310-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICARequest Interface"),
		pointer_default(unique)
	]
	interface ICARequest : IDispatch
	{
		[propget, id(1), helpstring("property RequestedItem")]		HRESULT RequestedItem([out, retval] IUnknown **ppTunereq);
		[propget, id(2), helpstring("property CAManager")]			HRESULT CAManager([out, retval] ICAManager **ppManager);
		[propget, id(3), helpstring("property ScheduleEntry")]		HRESULT ScheduleEntry([out, retval] IUnknown **ppUnkScheduleEntry);	// IScheduleEntry?
		[propget, id(4), helpstring("property Denials")]			HRESULT Denials([out, retval] ICADenials **ppDenials);
		[propget, id(5), helpstring("property Components")]			HRESULT Components([out, retval] ICAComponents **pComponents);
		[propget, id(6), helpstring("property Check")]				HRESULT Check([out, retval] long *pcDenials);
		[propget, id(7), helpstring("property ResolveDenials")]		HRESULT ResolveDenials([out, retval] long *pcDenials);
		[propget, id(8), helpstring("property CountDeniedComponents")]	HRESULT CountDeniedComponents([out, retval] long *pcDeniedComponents);
	};

	[
		object,
		uuid(11166320-DF8A-463a-B620-7BEC23542010),
		helpstring("ICAPolicy Interface"),				// client written
		pointer_default(unique)
	]
	interface ICAPolicy : IUnknown  // -- not IDispatch, let the client side implement that interface
	{
		[propget, id(0),   helpstring("property Name")]				HRESULT Name([out, retval] BSTR *pbstr);
		[         id(1),   helpstring("method CheckRequest")]		HRESULT CheckRequest([in] ICARequest *pReq);
//		[propget, id(2),   helpstring("property CAManager")]		HRESULT CAManager([out, retval] ICAManager **ppManager);
		[propput, id(2),   helpstring("property CAManager")]		HRESULT CAManager([in] ICAManager *pManager);
		[propget, id(3),   helpstring("property OkToPersist")]		HRESULT OkToPersist([out, retval] BOOL *pfOkToPersist);
		[propget, id(4),   helpstring("property OkToRemove")]		HRESULT OkToRemove([out, retval] BOOL *pfOkToRemove);
		[propget, id(5),   helpstring("property OkToRemoveDenial")]	HRESULT OkToRemoveDenial(ICADenial *pDenial, [out, retval] BOOL *pfOk);
		[propget, id(6),   helpstring("property OkToRemoveOffer")]	HRESULT OkToRemoveOffer(ICAOffer *pOffer, [out, retval] BOOL *pfOk);
	}
 
	[
		object,
		uuid(11166330-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAToll Interface"),				// also client written
		pointer_default(unique)
	]
	interface ICAToll : IUnknown	// -- also not IDispatch - client side implements that interface
	{
//		[propget, id(1), helpstring("property CAManager")]		HRESULT CAManager([out, retval] ICAManager **ppManager);
		[propput, id(1), helpstring("property CAManager")]		HRESULT CAManager([in] ICAManager *pManager);
		[         id(2), helpstring("method Select")]			HRESULT Select([in] BOOL fSelect);
		[         id(3), helpstring("method PayToll")]			HRESULT PayToll();
		[propget, id(4), helpstring("property Refundable")]		HRESULT Refundable([out, retval] BOOL *pfRefundable);
		[         id(5), helpstring("method RefundToll")]		HRESULT RefundToll();
		[propget, id(6), helpstring("property TolledObject")]	HRESULT TolledObject([out, retval] IUnknown **ppUnkTolled);
		[propget, id(7), helpstring("property Denials")]		HRESULT Denials([out, retval] ICADenials **ppDenials);
		[propget, id(8), helpstring("property Policy")]			HRESULT Policy([out, retval] ICAPolicy **ppPolicy);
		[propget, id(9), helpstring("property Description")]	HRESULT Description([in] CADescFormat enFormat, [out, retval] BSTR *pbstr);
		[propget, id(10),helpstring("property TimePaid")]		HRESULT TimePaid([out, retval] DATE *pdtPaid);
		[propget, id(11),helpstring("property State")]			HRESULT State([out, retval] CATollState *penState);
	};

	[
		object,
		uuid(11166340-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICADenial Interface"),
		pointer_default(unique)
	]
	interface ICADenial : IDispatch
	{
		[propget, id(1), helpstring("property DeniedObject")]	HRESULT DeniedObject([out, retval] IUnknown **ppUnkDenied);
		[propget, id(2), helpstring("property Policy")]			HRESULT Policy([out, retval] ICAPolicy **ppPolicy);
		[propget, id(3), helpstring("property Description")]	HRESULT Description([in] CADescFormat enFormat, [out, retval] BSTR *pbstr);
		[propput, id(3), helpstring("property Description")]	HRESULT Description([in] CADescFormat enFormat, [in] BSTR bstr);
		[propget, id(4), helpstring("property State")]			HRESULT State([out, retval] CADenialState *penState);
		[propput, id(4), helpstring("property State")]			HRESULT State([in] CADenialState enState);
		[propget, id(5), helpstring("property Tolls")]			HRESULT Tolls([out, retval] ICATolls **ppTolls);
		[         id(6),   helpstring("method NotifyTollStateChanged")]		HRESULT NotifyTollStateChanged([in] ICAToll *pToll, [in] CATollState enStateFrom);
	};
	
	[
		object,
		uuid(11166350-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAOffer Interface"),
		pointer_default(unique)
	]
	interface ICAOffer : IDispatch
	{
		[propget, id(1), helpstring("property CAManager")]		HRESULT CAManager([out, retval] ICAManager **ppManager);
		[propput, id(1), helpstring("property CAManager")]		HRESULT CAManager([in] ICAManager *pManager);
		[propget, id(2), helpstring("property Policy")]			HRESULT Policy([out, retval] ICAPolicy **pppolicy);
		[propget, id(3), helpstring("property Description")]	HRESULT Description([in] CADescFormat enFormat, [out, retval] BSTR *pbstr);
		[propput, id(3), helpstring("property Description")]	HRESULT Description([in] CADescFormat enFormat, [in] BSTR bstr);
		[propget, id(4), helpstring("property StartTime")]		HRESULT StartTime([out, retval] DATE *pdtStart);
		[propget, id(5), helpstring("property EndTime")]		HRESULT EndTime([out, retval] DATE *pdtEnd);
		[propget, id(6), helpstring("property Tolls")]			HRESULT Tolls([out, retval] ICATolls **ppTolls);
		[         id(7), helpstring("method NotifyTollStateChanged")]		HRESULT NotifyTollStateChanged([in] ICAToll *pToll, [in] CATollState enStateFrom);
	};

	[
		object,
		uuid(11166360-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAComponent Interface"),
		pointer_default(unique)
	]
	interface ICAComponent : IDispatch
	{
		[propget, id(1), helpstring("property Component")]		HRESULT Component([out, retval] IComponent **ppComponent);
		[propget, id(2), helpstring("property Denials")]		HRESULT Denials([out, retval] ICADenials **ppDenials);
		[propget, id(3), helpstring("property Request")]		HRESULT Request([out, retval] ICARequest **ppComponent);
	};

		// --------------------------------

	[
		uuid(11166100-DF8A-463a-B620-7BEC23542010),
		helpstring("CAManager Class")
	]
	coclass CAManager
	{
		[default] interface ICAManager;
		interface ICAManagerInternal;
//		interface ICAManagerXProxy;                             // this is the actual outgoing 'event' interface
		[default, source] dispinterface _ICAManagerEvents;
	};

	[				// magic class used to avoid circular references through the CAManager.
		hidden,
		uuid(11166101-DF8A-463a-B620-7BEC23542010),
		helpstring("CAManagerProxy Class")
	]
	coclass CAManagerProxy
	{
		[default] interface ICAManager;
		interface ICAManagerInternal;
//		[default, source] dispinterface _ICAManagerEvents;  /// QUESTION ??? DO I want these just here, or in the true Manager, or both?
	};

	[
		uuid(11166540-DF8A-463a-B620-7BEC23542010),			// needed for user's tolls.
		helpstring("CADenials Class")
	]
	coclass CADenials
	{
		[default] interface ICADenials;
		interface ICADenialsInternal;
		[default, source] dispinterface _ICADenialsEvents;
	};
	
						// TODO - mark this as hidden...
	[
		uuid(11166550-DF8A-463a-B620-7BEC23542010),
		helpstring("CAOffer Class")
	]
	coclass CAOffer
	{
		[default] interface ICAOffer;
	}; 


/*	[
		uuid(11166999-DF8A-463a-B620-7BEC23542010),
		helpstring("CATempEvent_TempBuilder Class - used so we can build events the first time")
	]
	coclass CATempEvent_TempBuilder
	{
		[source] dispinterface _ICAResDenialTreeEvents;
		[source] dispinterface _ICAManagerEvents;
		[source] dispinterface _ICARequestEvents;
		[source] dispinterface _ICATollsEvents;
		[source] dispinterface _ICADenialsEvents;
		[source] dispinterface _ICAPoliciesEvents;
		[source] dispinterface _ICAOffersEvents;
		[source] dispinterface _ICAComponentsEvents;
	};
*/
			// -----------------------------------
	[
		object,
		uuid(11166898-DF8A-463a-B620-7BEC23542010),
		dual,
		helpstring("ICAResDenialTree Interface"),
		pointer_default(unique)
	]
	interface ICAResDenialTree : IDispatch
	{
		[propget, id(1),   helpstring("property CAManager")]					HRESULT CAManager([out, retval] ICAManager **ppCAManager);
		[propput, id(1),   helpstring("property CAManager")]					HRESULT CAManager([in] ICAManager *pCAManager);
		[propget, id(2),   helpstring("property DisplayFields")]				HRESULT DisplayFields([out, retval] long *penFields);	// fields to display in default ResDenial UI
		[propput, id(2),   helpstring("property DisplayFields")]				HRESULT DisplayFields([in] long enFields);				// fields to display in default ResDenial UI
		[         id(3),   helpstring("method UpdateView")]						HRESULT UpdateView([in] IUnknown *pUnk);				// if object is being viewed, updates it.
				// ResDenial control listens to these incoming sink events sent by Manager ((_ICAManagerEvents).. ID's Must match 
		[		  id(2201),helpstring("method NotifyRequestActivated")]			HRESULT NotifyRequestActivated([in] ICARequest *preq);
		[		  id(2202),helpstring("method NotifyRequestDeactivated")]		HRESULT NotifyRequestDeactivated([in] ICARequest *preq);
		[		  id(2203),helpstring("method NotifyOfferAdded")]				HRESULT NotifyOfferAdded([in] ICAOffer *pOffer,[in] long cOffers);
		[		  id(2204),helpstring("method NotifyOfferRemoved")]				HRESULT NotifyOfferRemoved([in] ICAOffer *pOffer,[in] long cOffers);
		[		  id(2205),helpstring("method NotifyPolicyAdded")]				HRESULT NotifyPolicyAdded([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[		  id(2206),helpstring("method NotifyPolicyRemoved")]			HRESULT NotifyPolicyRemoved([in] ICAPolicy *pPolicy,[in] long cPolicies);
		[		  id(2207),helpstring("method NotifyRequestDenialAdded")]		HRESULT NotifyRequestDenialAdded([in] ICARequest *preq, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2208),helpstring("method NotifyRequestDenialRemoved")]		HRESULT NotifyRequestDenialRemoved([in] ICARequest *preq, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2209),helpstring("method NotifyDenialTollAdded")]			HRESULT NotifyDenialTollAdded([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2210),helpstring("method NotifyDenialTollRemoved")]		HRESULT NotifyDenialTollRemoved([in] ICADenial *pDenial, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2211),helpstring("method NotifyTollDenialAdded")]			HRESULT NotifyTollDenialAdded([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2212),helpstring("method NotifyTollDenialRemoved")]		HRESULT NotifyTollDenialRemoved([in] ICAToll *pToll, [in] ICADenial *pDenial,[in] long cDenials);
		[		  id(2213),helpstring("method NotifyOfferTollAdded")]			HRESULT NotifyOfferTollAdded([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[		  id(2214),helpstring("method NotifyOfferTollRemoved")]			HRESULT NotifyOfferTollRemoved([in] ICAOffer *pOffer, [in] ICAToll *pToll, [in] long cTolls);
		[         id(2215),helpstring("method NotifyTollStateChanged")]			HRESULT NotifyTollStateChanged([in] ICAToll *pToll, [in] CATollState enStateLast);
		[         id(2216),helpstring("method NotifyDenialStateChanged")]		HRESULT NotifyDenialStateChanged([in] ICADenial *pDenial, [in] CADenialState enStateLast);
		[         id(2217),helpstring("method NotifyComponentDenialAdded")]		HRESULT NotifyComponentDenialAdded([in] ICAComponent *preq, [in] ICADenial *pDenial,[in] long cDenials);
		[         id(2218),helpstring("method NotifyComponentDenialRemoved")]	HRESULT NotifyComponentDenialRemoved([in] ICAComponent *preq, [in] ICADenial *pDenial,[in] long cDenials);
	};

	[
		uuid(11166998-DF8A-463a-B620-7BEC23542010),
		helpstring("CA Default RequestDenial Control")
	]
	coclass CAResDenialTree
	{
		[default] interface ICAResDenialTree;
		[default, source] dispinterface _ICAResDenialTreeEvents;
	};

				// -----------------------------

    [
            uuid(11166991-DF8A-463a-B620-7BEC23542010),
            helpstring("_ICADefaultDlgEvents Interface")
    ]
    dispinterface _ICADefaultDlgEvents
    {
            properties:
            methods:
    };

    [
            uuid(11166990-DF8A-463a-B620-7BEC23542010),
            helpstring("CADefaultDlg Class")
    ]
    coclass CADefaultDlg
    {
            [default] interface ICADefaultDlg;
            [default, source] dispinterface _ICADefaultDlgEvents;
    };

			// -------------------------------------
			//  Magic way to define the SID_ ...

	cpp_quote("#define SID_CAManager CLSID_CAManager")
};
