﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgL|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgLDeb|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgMDeb|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgS|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgSDeb|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgXL|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='CgXLDeb|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|Win32'">
    <LocalDebuggerCommandArguments>CGstart</LocalDebuggerCommandArguments>
    <LocalDebuggerWorkingDirectory>
    </LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <LocalDebuggerCommand>$(ProjectName).exe</LocalDebuggerCommand>
  </PropertyGroup>
</Project>