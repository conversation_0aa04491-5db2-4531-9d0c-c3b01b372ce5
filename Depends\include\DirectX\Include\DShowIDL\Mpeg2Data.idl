/////////////////////////////////////////////////////////////////////////////
//
// Copyright (c) Microsoft Corporation.  All rights reserved.
//
// Module Name:
//
//      Mpeg2Data.idl
//
// Abstract:
//
//      Main Mpeg2Data Library Definition, and interface definitions for
//      the MPEG-2 Section and Table acquisition functionality
//
/////////////////////////////////////////////////////////////////////////////

    // Import Files
import "oaidl.idl";
import "ocidl.idl";
import "bdaiface.idl";

    // Specify single byte packing alignment
#pragma pack(push)
#pragma pack(1)

    // Forward interface declarations
interface ISectionList;
interface IMpeg2Stream;

    // Declare well known PID/TID values for MPEG-2 tables
cpp_quote("#define MPEG_PAT_PID                0x0000")
cpp_quote("#define MPEG_PAT_TID                0x00")

cpp_quote("#define MPEG_CAT_PID                0x0001")
cpp_quote("#define MPEG_CAT_TID                0x01")

cpp_quote("#define MPEG_PMT_TID                0x02")

cpp_quote("#define MPEG_TSDT_PID               0x0002")
cpp_quote("#define MPEG_TSDT_TID               0x03")

    // Declare well known PID/TID values for ATSC tables
cpp_quote("#define ATSC_MGT_PID                0x1FFB")
cpp_quote("#define ATSC_MGT_TID                0xC7")

cpp_quote("#define ATSC_VCT_PID                0x1FFB")
cpp_quote("#define ATSC_VCT_TERR_TID           0xC8")
cpp_quote("#define ATSC_VCT_CABL_TID           0xC9")

cpp_quote("#define ATSC_RRT_PID                0x1FFB")
cpp_quote("#define ATSC_RRT_TID                0xCA")

cpp_quote("#define ATSC_EIT_TID                0xCB")

cpp_quote("#define ATSC_ETT_TID                0xCC")

cpp_quote("#define ATSC_STT_PID                0x1FFB")
cpp_quote("#define ATSC_STT_TID                0xCD")

cpp_quote("#define ATSC_PIT_TID                0xD0")

    // Declare well known PID/TID values for DVB tables
cpp_quote("#define DVB_NIT_PID                 0x0010")
cpp_quote("#define DVB_NIT_ACTUAL_TID          0x40")
cpp_quote("#define DVB_NIT_OTHER_TID           0x41")

cpp_quote("#define DVB_SDT_PID                 0x0011")
cpp_quote("#define DVB_SDT_ACTUAL_TID          0x42")
cpp_quote("#define DVB_SDT_OTHER_TID           0x46")

cpp_quote("#define DVB_BAT_PID                 0x0011")
cpp_quote("#define DVB_BAT_TID                 0x4A")

cpp_quote("#define DVB_EIT_PID                 0x0012")
cpp_quote("#define DVB_EIT_ACTUAL_TID          0x4E")
cpp_quote("#define DVB_EIT_OTHER_TID           0x4F")

cpp_quote("#define DVB_RST_PID                 0x0013")
cpp_quote("#define DVB_RST_TID                 0x71")

cpp_quote("#define DVB_TDT_PID                 0x0014")
cpp_quote("#define DVB_TDT_TID                 0x70")

cpp_quote("#define DVB_ST_PID_16               0x0010")
cpp_quote("#define DVB_ST_PID_17               0x0011")
cpp_quote("#define DVB_ST_PID_18               0x0012")
cpp_quote("#define DVB_ST_PID_19               0x0013")
cpp_quote("#define DVB_ST_PID_20               0x0014")
cpp_quote("#define DVB_ST_TID                  0x72")

cpp_quote("#define DVB_TOT_PID                 0x0014")
cpp_quote("#define DVB_TOT_TID                 0x73")

cpp_quote("#define DVB_DIT_PID                 0x001E")
cpp_quote("#define DVB_DIT_TID                 0x7E")

cpp_quote("#define DVB_SIT_PID                 0x001F")
cpp_quote("#define DVB_SIT_TID                 0x7F")

    // Declare well known PID/TID values for ISDB tables
cpp_quote("#define ISDB_DCT_PID                0x0017")
cpp_quote("#define ISDB_DCT_TID                0xC0")

cpp_quote("#define ISDB_LIT_PID                0x0020")
cpp_quote("#define ISDB_LIT_TID                0xD0")

cpp_quote("#define ISDB_ERT_PID                0x0021")
cpp_quote("#define ISDB_ERT_TID                0xD1")

cpp_quote("#define ISDB_ITT_TID                0xD2")

cpp_quote("#define ISDB_DLT_TID                0xC1")

cpp_quote("#define ISDB_PCAT_PID               0x0022")
cpp_quote("#define ISDB_PCAT_TID               0xC2")

cpp_quote("#define ISDB_SDTT_PID               0x0023")
cpp_quote("#define ISDB_SDTT_TID               0xC3")



////////////////////////////////////
//
// Mpeg2DataLib Library
//
////////////////////////////////////

cpp_quote("class DECLSPEC_UUID(\"DBAF6C1B-B6A4-4898-AE65-204F0D9509A1\") Mpeg2DataLib;")

[
    uuid(DBAF6C1B-B6A4-4898-AE65-204F0D9509A1),
    version(1.0)
]
library Mpeg2DataLib
{
    importlib("stdole32.tlb");
    importlib("stdole2.tlb");

    // Include related interface definition files so that everything ends up
    // in the same library. Note that the order in which these files are
    // included is important, so do not rearrange them arbitrarily
#include "Mpeg2Structs.idl"
#ifdef MPEG2_FUTURE_CODE // Not available in DX9
#include "Mpeg2PsiParser.idl"
#include "AtscPsipParser.idl"
#include "DvbSiParser.idl"
#endif



    ////////////////////////////////////
    //
    // IMpeg2Data Interface
    //
    ////////////////////////////////////

    [
        object,
        uuid(9B396D40-F380-4e3c-A514-1A82BF6EBFE6),    
        pointer_default(unique)
    ]
    interface IMpeg2Data : IUnknown
    {
        HRESULT GetSection([in]  PID                      pid,
                           [in]  TID                      tid,
                           [in]  PMPEG2_FILTER            pFilter,            // OPTIONAL
                           [in]  DWORD                    dwTimeout,
                           [out] ISectionList **          ppSectionList);

        HRESULT GetTable([in]  PID                        pid,
                         [in]  TID                        tid,
                         [in]  PMPEG2_FILTER              pFilter,            // OPTIONAL
                         [in]  DWORD                      dwTimeout,
                         [out] ISectionList **            ppSectionList);

        HRESULT GetStreamOfSections([in]  PID             pid,
                                    [in]  TID             tid,
                                    [in]  PMPEG2_FILTER   pFilter,            // OPTIONAL
                                    [in]  HANDLE          hDataReadyEvent,
                                    [out] IMpeg2Stream ** ppMpegStream);
    };



    ////////////////////////////////////
    //
    // ISectionList Interface
    //
    ////////////////////////////////////

    [
        object,
        uuid(AFEC1EB5-2A64-46c6-BF4B-AE3CCB6AFDB0),
        pointer_default(unique)
    ]
    interface ISectionList : IUnknown
    {
        HRESULT Initialize([in]  MPEG_REQUEST_TYPE requestType,
                           [in]  IMpeg2Data *      pMpeg2Data,
                           [in]  PMPEG_CONTEXT     pContext,
                           [in]  PID               pid,
                           [in]  TID               tid,
                           [in]  PMPEG2_FILTER     pFilter,                   // OPTIONAL
                           [in]  DWORD             timeout,
                           [in]  HANDLE            hDoneEvent);               // OPTIONAL

        HRESULT InitializeWithRawSections([in]  PMPEG_PACKET_LIST pmplSections);

        HRESULT CancelPendingRequest(void);

        HRESULT GetNumberOfSections([out] WORD * pCount);

        HRESULT GetSectionData([in]  WORD          sectionNumber,
                               [out] DWORD *       pdwRawPacketLength,
                               [out] PSECTION *    ppSection);

        HRESULT GetProgramIdentifier(PID * pPid);

        HRESULT GetTableIdentifier(TID * pTableId);
    };



    ////////////////////////////////////
    //
    // IMpeg2Stream Interface
    //
    ////////////////////////////////////

    [
        object,
        uuid(400CC286-32A0-4ce4-9041-39571125A635),
        pointer_default(unique)
    ]
    interface IMpeg2Stream : IUnknown
    {
        HRESULT Initialize([in]  MPEG_REQUEST_TYPE         requestType,
                           [in]  IMpeg2Data *              pMpeg2Data,
                           [in]  PMPEG_CONTEXT             pContext,
                           [in]  PID                       pid,
                           [in]  TID                       tid,
                           [in]  PMPEG2_FILTER             pFilter,           // OPTIONAL
                           [in]  HANDLE                    hDataReadyEvent);

        HRESULT SupplyDataBuffer([in]  PMPEG_STREAM_BUFFER pStreamBuffer);
    };



    ////////////////////////////////////
    //
    // SectionList CoClass
    //
    ////////////////////////////////////

    [
        uuid(73DA5D04-4347-45d3-A9DC-FAE9DDBE558D)
    ]
    coclass SectionList
    {
        [default] interface ISectionList;
    };

    

    ////////////////////////////////////
    //
    // Mpeg2Stream CoClass
    //
    ////////////////////////////////////

    [
        uuid(F91D96C7-8509-4d0b-AB26-A0DD10904BB7)
    ]
    coclass Mpeg2Stream
    {
        [default] interface IMpeg2Stream;
    };

    

    ////////////////////////////////////
    //
    // Mpeg2Data CoClass
    //
    ////////////////////////////////////

    [
        uuid(C666E115-BB62-4027-A113-82D643FE2D99)
    ]
    coclass Mpeg2Data
    {
        [default] interface IMpeg2Data;
#ifdef MPEG2_FUTURE_CODE // Not available in DX9
        interface IAtscPsipParser;
        interface IDvbSiParser;
#endif
    };
};



    // Return to default packing
#pragma pack(pop)
