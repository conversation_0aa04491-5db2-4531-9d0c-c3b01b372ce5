﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{cc34035a-8d02-4739-9a14-7db935f1913b}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{30b60685-cadb-4e6b-9f8f-dfc05231ece3}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{09c4f217-faad-41c0-a0ca-b3d858fe97be}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="puk2">
      <UniqueIdentifier>{6d7c4e27-a22e-42a7-8c23-1590bcbd7e09}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\newBattle">
      <UniqueIdentifier>{707139dc-ed93-4203-8a84-21d5736f3d48}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\newDraw">
      <UniqueIdentifier>{681e2aee-03a9-444e-93d3-ea360a9eaebc}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\newDraw\txt">
      <UniqueIdentifier>{688f442a-5f5f-4e9a-b7fe-43065e7804d1}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\newMap">
      <UniqueIdentifier>{4ab6b87a-794d-45b1-a1da-74a59ee0d89d}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\guild">
      <UniqueIdentifier>{e5285ea0-2e0b-44aa-a233-934254dc4342}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface">
      <UniqueIdentifier>{27f438fa-d8dd-4041-89f0-cef624319999}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Cockpit">
      <UniqueIdentifier>{6ee1472c-d181-46fc-92ef-1e928ec48374}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Address">
      <UniqueIdentifier>{bda9f9f4-4a52-4751-a874-741220880d92}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Item">
      <UniqueIdentifier>{4fc84dd3-1328-450d-a46b-f5c70753db0d}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Battle">
      <UniqueIdentifier>{beffcdb2-c276-4e63-8755-f7b86a71118b}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Guild No. 1">
      <UniqueIdentifier>{f88b2911-5c81-4d36-a6a4-434279fd4bb1}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\System">
      <UniqueIdentifier>{8da17cdb-25c5-48fd-bbb7-3c5420ca0053}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Status">
      <UniqueIdentifier>{2cb0393d-9482-4dcd-917a-3942d4217644}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Shop">
      <UniqueIdentifier>{6ea922f3-0399-4dfa-a6ed-7c81ec7e002e}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk2\interface\Login">
      <UniqueIdentifier>{810f999f-5732-4393-9afb-487f712990c5}</UniqueIdentifier>
    </Filter>
    <Filter Include="puk3">
      <UniqueIdentifier>{6ef437d9-ffc0-4e2c-bdcd-1e91076f866f}</UniqueIdentifier>
    </Filter>
    <Filter Include="mlstring">
      <UniqueIdentifier>{d897e269-bff6-4ba0-a56a-1ea4509da239}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="system\action.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\battle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\battleEffect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\battleMap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\battleMaster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\battleMenu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\battleProc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\character.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\chat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\debugLogWin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="crypto\desc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\direct3D.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\directDraw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\dmctrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\field.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\filetbl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\font.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\gamemain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\getdxver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\handletime.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\ime.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\keyboard.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\litchi_base64.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\loadrealbin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\loadsprbin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\login.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\map.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\mapEffect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\mapGridCursol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\math2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\menu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\menu_appendjewel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\menu_fishing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\mouse.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\netmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\netproc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\nrproto_cli.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\nrproto_util.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ohta\Ohta.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ohta\ohta_proc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\Pattern.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\pc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\process.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\produce.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="crypto\r_stdlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\savedata.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\Sprdisp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\sprmgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\t_music.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\testView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\tool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="system\unpack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="puk2\newBattle\battleTrance.cpp">
      <Filter>puk2\newBattle</Filter>
    </ClCompile>
    <ClCompile Include="puk2\newBattle\newBattle.cpp">
      <Filter>puk2\newBattle</Filter>
    </ClCompile>
    <ClCompile Include="PUK2\newDraw\directdraw3D.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="puk2\newDraw\newcharacter.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="PUK2\newDraw\newPattern.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="puk2\newDraw\newpc.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="PUK2\newDraw\newSprdisp.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="PUK2\newDraw\newsprmgr.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="puk2\newDraw\Save_bmp.cpp">
      <Filter>puk2\newDraw</Filter>
    </ClCompile>
    <ClCompile Include="puk2\map\newmap.cpp">
      <Filter>puk2\newMap</Filter>
    </ClCompile>
    <ClCompile Include="puk2\guild\guild.cpp">
      <Filter>puk2\guild</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuShortcut.cpp">
      <Filter>puk2\interface</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuwin.cpp">
      <Filter>puk2\interface</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuWinEtcObj.cpp">
      <Filter>puk2\interface</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuAction.cpp">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuchat.cpp">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menucockpitL.cpp">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menucockpitR.cpp">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menumap.cpp">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menumenu.cpp">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuAddress.cpp">
      <Filter>puk2\interface\Address</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuAddressBook.cpp">
      <Filter>puk2\interface\Address</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuMail.cpp">
      <Filter>puk2\interface\Address</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuBank.cpp">
      <Filter>puk2\interface\Item</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuItem.cpp">
      <Filter>puk2\interface\Item</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuTrade.cpp">
      <Filter>puk2\interface\Item</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menubattle.cpp">
      <Filter>puk2\interface\Battle</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuGuild.cpp">
      <Filter>puk2\interface\Guild No. 1</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuAlbum.cpp">
      <Filter>puk2\interface\System</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuGeneral.cpp">
      <Filter>puk2\interface\System</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menumultipurpose.cpp">
      <Filter>puk2\interface\System</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuServerrequest.cpp">
      <Filter>puk2\interface\System</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuSystem.cpp">
      <Filter>puk2\interface\System</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuMonster.cpp">
      <Filter>puk2\interface\Status</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuSkill.cpp">
      <Filter>puk2\interface\Status</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\MenuStatus.cpp">
      <Filter>puk2\interface\Status</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\menuShop.cpp">
      <Filter>puk2\interface\Shop</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\charamake_PUK2.cpp">
      <Filter>puk2\interface\Login</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\loginCharaSel.cpp">
      <Filter>puk2\interface\Login</Filter>
    </ClCompile>
    <ClCompile Include="puk2\interface\loginTitle.cpp">
      <Filter>puk2\interface\Login</Filter>
    </ClCompile>
    <ClCompile Include="puk3\account\account.cpp">
      <Filter>puk3</Filter>
    </ClCompile>
    <ClCompile Include="puk3\interface\loginTitlePuk3.cpp">
      <Filter>puk3</Filter>
    </ClCompile>
    <ClCompile Include="puk3\profile\profile.cpp">
      <Filter>puk3</Filter>
    </ClCompile>
    <ClCompile Include="puk3\character\WhaleShip.cpp">
      <Filter>puk3</Filter>
    </ClCompile>
    <ClCompile Include="mlstring\MlString.cpp">
      <Filter>mlstring</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Script2.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="crypto\des.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\directDraw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\filetbl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="crypto\global.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\litchi_base64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="crypto\rsa_incl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="puk2\c_option.h">
      <Filter>puk2</Filter>
    </ClInclude>
    <ClInclude Include="PUK2\puk2.h">
      <Filter>puk2</Filter>
    </ClInclude>
    <ClInclude Include="puk2\newBattle\newBattle.h">
      <Filter>puk2\newBattle</Filter>
    </ClInclude>
    <ClInclude Include="puk2\newDraw\anim_tbl_PUK2.h">
      <Filter>puk2\newDraw</Filter>
    </ClInclude>
    <ClInclude Include="PUK2\newDraw\BLT_MEMBER.H">
      <Filter>puk2\newDraw</Filter>
    </ClInclude>
    <ClInclude Include="puk2\newDraw\Graphic_ID.h">
      <Filter>puk2\newDraw</Filter>
    </ClInclude>
    <ClInclude Include="puk2\map\newmap.h">
      <Filter>puk2\newMap</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\guild.h">
      <Filter>puk2\guild</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuShortcut.h">
      <Filter>puk2\interface</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuwin.h">
      <Filter>puk2\interface</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuwinonelineinfo.h">
      <Filter>puk2\interface</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuAction.h">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuchat.h">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menucockpitL.h">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menucockpitR.h">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menumap.h">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menumenu.h">
      <Filter>puk2\interface\Cockpit</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuAddress.h">
      <Filter>puk2\interface\Address</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuAddressBook.h">
      <Filter>puk2\interface\Address</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuMail.h">
      <Filter>puk2\interface\Address</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuBank.h">
      <Filter>puk2\interface\Item</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuItem.h">
      <Filter>puk2\interface\Item</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuTrade.h">
      <Filter>puk2\interface\Item</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menubattle.h">
      <Filter>puk2\interface\Battle</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuGuild.h">
      <Filter>puk2\interface\Guild No. 1</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuAlbum.h">
      <Filter>puk2\interface\System</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuGeneral.h">
      <Filter>puk2\interface\System</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuMultipurpose.h">
      <Filter>puk2\interface\System</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuServerrequest.h">
      <Filter>puk2\interface\System</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuSystem.h">
      <Filter>puk2\interface\System</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuMonster.h">
      <Filter>puk2\interface\Status</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuSkill.h">
      <Filter>puk2\interface\Status</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\MenuStatus.h">
      <Filter>puk2\interface\Status</Filter>
    </ClInclude>
    <ClInclude Include="puk2\interface\menuShop.h">
      <Filter>puk2\interface\Shop</Filter>
    </ClInclude>
    <ClInclude Include="puk3\interface\loginTitlePuk3.h">
      <Filter>puk3</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mlstring\MlString.h">
      <Filter>mlstring</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\language.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\lang_tw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="systeminc\lang_cn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="cursor2.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursor3.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursor4.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir0.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir1.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir2.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir3.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir4.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir5.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir6.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="cursorDir7.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="mouse.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="sa_mouse.cur">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="anim_cur.ani" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="icon1.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="icon2.ico">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <Text Include="PUK2\newDraw\Readme.txt">
      <Filter>puk2\newDraw\txt</Filter>
    </Text>
  </ItemGroup>
  <ItemGroup>
    <Xml Include="mlstring\CrossGate_ml.xml">
      <Filter>mlstring</Filter>
    </Xml>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="mlstring\Resources.resx">
      <Filter>mlstring</Filter>
    </EmbeddedResource>
  </ItemGroup>
</Project>