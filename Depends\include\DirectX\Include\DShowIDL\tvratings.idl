// TvRatings.idl : IDL source for TvRatings.dll
//

// This file will be processed by the MIDL tool to
// produce the type library (TvRatings.tlb) and marshalling code.

import "oaidl.idl";
import "ocidl.idl";

typedef enum 
{
	MPAA					= 0,
	US_TV					= 1,
	Canadian_English		= 2,
	Canadian_French			= 3,
	Reserved4				= 4,	// filler - not used.
	System5					= 5,	// maps to system 5 of XDS rating table 19
	System6					= 6,	// maps to system 6 of XDS rating table 19
	Reserved7				= 7,	// filler - not used.
	TvRat_kSystems			= 8,	// used for allocating structures			
	TvRat_SystemDontKnow	= 255	// haven't gotten a data value yet... (perhaps change to 0)
} EnTvRat_System;


typedef enum		// constraint is that must be in increasing order, and max < 2^8
{
	TvRat_0					= 0,
	TvRat_1					= 1,
	TvRat_2					= 2,
	TvRat_3					= 3,
	TvRat_4					= 4,
	TvRat_5					= 5,
	TvRat_6					= 6,
	TvRat_7					= 7,
	TvRat_kLevels			= 8,		// used for allocating structures			
	TvRat_LevelDontKnow		= 255		// haven't gotten a data value yet... (perhaps change to _7)
} EnTvRat_GenericLevel;


typedef enum 
{
	MPAA_NotApplicable		= TvRat_0,
	MPAA_G					= TvRat_1,
	MPAA_PG					= TvRat_2,
	MPAA_PG13				= TvRat_3,
	MPAA_R					= TvRat_4,
	MPAA_NC17				= TvRat_5,
	MPAA_X					= TvRat_6,
	MPAA_NotRated			= TvRat_7					
} EnTvRat_MPAA;

typedef enum 
{
	US_TV_None				= TvRat_0,
	US_TV_Y					= TvRat_1,
	US_TV_Y7				= TvRat_2,
	US_TV_G					= TvRat_3,
	US_TV_PG				= TvRat_4,
	US_TV_14				= TvRat_5,
	US_TV_MA				= TvRat_6,
	US_TV_None7				= TvRat_7					
} EnTvRat_US_TV;

typedef enum 
{
	CAE_TV_Exempt			= TvRat_0,
	CAE_TV_C				= TvRat_1,
	CAE_TV_C8				= TvRat_2,
	CAE_TV_G				= TvRat_3,
	CAE_TV_PG				= TvRat_4,
	CAE_TV_14				= TvRat_5,
	CAE_TV_18				= TvRat_6,
	CAE_TV_Reserved			= TvRat_7					
} EnTvRat_CAE_TV;

typedef enum 
{
    CAF_TV_Exempt			= TvRat_0,
    CAF_TV_G				= TvRat_1,
    CAF_TV_8				= TvRat_2,
    CAF_TV_13				= TvRat_3,
    CAF_TV_16				= TvRat_4,
    CAF_TV_18				= TvRat_5,
    CAF_TV_Reserved6		= TvRat_6,
    CAF_TV_Reserved			= TvRat_7					
} EnTvRat_CAF_TV;

	
		// -------------------
typedef enum 
{
    BfAttrNone                      = 0,	// no bits set (for initialization)			
    BfIsBlocked                     = 1,	// if set, 
    BfIsAttr_1                      = 2,
    BfIsAttr_2                      = 4,	
    BfIsAttr_3                      = 8,
    BfIsAttr_4                      = 16,		
    BfIsAttr_5                      = 32,	// no bits set...
    BfIsAttr_6                      = 64,
    BfIsAttr_7                      = 128,	
    BfValidAttrSubmask              = 255		// IsBlocked is not a valid attribute to display	
} BfEnTvRat_GenericAttributes;

typedef enum 
{
    US_TV_IsBlocked                     = BfIsBlocked,
    US_TV_IsViolent                     = BfIsAttr_1,
    US_TV_IsSexualSituation             = BfIsAttr_2,
    US_TV_IsAdultLanguage               = BfIsAttr_3,
    US_TV_IsSexuallySuggestiveDialog    = BfIsAttr_4,
    US_TV_ValidAttrSubmask              = 31	// IsBlocked is not a valid attribute for TV
} BfEnTvRat_Attributes_US_TV;

typedef enum 
{
    MPAA_IsBlocked	          			= BfIsBlocked,
    MPAA_ValidAttrSubmask				= 1		// IsBlocked is not a valid attribute 
} BfEnTvRat_Attributes_MPAA;

typedef enum 
{
    CAE_IsBlocked                       = BfIsBlocked,
    CAE_ValidAttrSubmask                = 1		// IsBlocked is not a valid attribute 
} BfEnTvRat_Attributes_CAE_TV;

typedef enum 
{
    CAF_IsBlocked	          			= BfIsBlocked,
    CAF_ValidAttrSubmask				= 1		// IsBlocked is not a valid attribute 
} BfEnTvRat_Attributes_CAF_TV;

		// -------------------------------------------------------
		// -------------------------------------------------------
[
    object,
    uuid(C5C5C5B0-3ABC-11D6-B25B-00C04FA0C026),
    dual,
    helpstring("IXDSToRat Interface"),
    pointer_default(unique)
]
interface IXDSToRat : IDispatch
{
    [id(1),  helpstring("method Init")]
        HRESULT Init();
		
    [id(2),  helpstring("method ParseXDSBytePair")]  
        HRESULT ParseXDSBytePair(
            [in]  BYTE                          byte1, 
            [in]  BYTE                          byte2, 
            [out] EnTvRat_System                *pEnSystem,
            [out] EnTvRat_GenericLevel          *pEnLevel,
            [out] LONG                          *plBfEnAttributes        // bitfield of BfEnTvRat_GenericAttributes
            );
};

	// ------------------------
[
	object,
	uuid(C5C5C5B1-3ABC-11D6-B25B-00C04FA0C026),
	dual,
	helpstring("IEvalRat Interface"),
	pointer_default(unique)
]
interface IEvalRat : IDispatch
{
    [propget, id(1), helpstring("property BlockedRatingAttributes")] 
        HRESULT BlockedRatingAttributes(
            [in] EnTvRat_System                 enSystem, 
            [in] EnTvRat_GenericLevel           enLevel,
            [out, retval] LONG                  *plbfAttrs     // bitfield of BfEnTvRat_GenericAttributes
            );

    [propput, id(1), helpstring("property BlockedRatingAttributes")] 
        HRESULT BlockedRatingAttributes(
            [in] EnTvRat_System                 enSystem, 
            [in] EnTvRat_GenericLevel           enLevel,
            [in] LONG                           lbfAttrs                // bitfield of BfEnTvRat_GenericAttributes
            );

	[propget, id(2), helpstring("property BlockUnRated")] 
        HRESULT BlockUnRated( 
            [out, retval] BOOL          *pfBlockUnRatedShows
            );

	[propput, id(2), helpstring("property BlockUnRated")] 
        HRESULT BlockUnRated(
            [in] BOOL                   fBlockUnRatedShows
            );

	[   id(3), helpstring("method MostRestrictiveRating")] 
        HRESULT MostRestrictiveRating(
            [in] EnTvRat_System                 enSystem1, 
            [in] EnTvRat_GenericLevel           enEnLevel1,
            [in] LONG                           lbfEnAttr1,                 // bitfield of BfEnTvRat_GenericAttributes
            [in] EnTvRat_System                 enSystem2, 
            [in] EnTvRat_GenericLevel           enEnLevel2,
            [in] LONG                           lbfEnAttr2,                // bitfield of BfEnTvRat_GenericAttributes
           [out] EnTvRat_System                 *penSystem, 
           [out] EnTvRat_GenericLevel           *penEnLevel,
           [out] LONG                           *plbfEnAttr                // bitfield of BfEnTvRat_GenericAttributes
			);

	[   id(4), helpstring("method TestRating")]  
        HRESULT TestRating(
            [in] EnTvRat_System                 enShowSystem,
            [in] EnTvRat_GenericLevel           enShowLevel,
            [in] LONG                           lbfEnShowAttributes       // bitfield of BfEnTvRat_GenericAttributes
            );

};

        // This is enforcing a particular implementation on folk...
        //   Don't need this particular TypeLib, just as long
        //   as some library/libraries exists that supports the two coClasses,
        //   with the give ClassID's.
[
    uuid(C5C5C500-3ABC-11D6-B25B-00C04FA0C026),
    version(1.0),
    helpstring("TvRatings 1.0 Type Library")
]
library TVRATINGSLib
{
    importlib("stdole32.tlb");
    importlib("stdole2.tlb");

    [
        uuid(C5C5C5F0-3ABC-11D6-B25B-00C04FA0C026),
        helpstring("XDSToRat Class")
    ]
    coclass XDSToRat
    {
        [default] interface IXDSToRat;
    };
    [
        uuid(C5C5C5F1-3ABC-11D6-B25B-00C04FA0C026),
        helpstring("EvalRat Class")
    ]
    coclass EvalRat
    {
        [default] interface IEvalRat;
    };
};
