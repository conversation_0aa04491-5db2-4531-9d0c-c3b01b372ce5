//------------------------------------------------------------------------------
// File: StrmIf.idl
//
// Desc: ActiveMovie interface definitions.
//
// Copyright (c) 1992 - 2002, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------

//
// Temporarily disable the /W4 compiler warning C4201, which will be 
// reported by all apps which include streams.h or dshow.h, which both
// include the strmif.h generated by this IDL file.
//
cpp_quote("//+-------------------------------------------------------------------------")
cpp_quote("//")
cpp_quote("//  Copyright (C) Microsoft Corporation, 1999-2002.")
cpp_quote("//")
cpp_quote("//--------------------------------------------------------------------------")
cpp_quote("// Disable /W4 compiler warning C4201: nameless struct/union")
cpp_quote("#pragma warning(push)")
cpp_quote("#pragma warning(disable:4201)  // Disable C4201: nameless struct/union")
cpp_quote("  ")   // Blank space


import "unknwn.idl";
import "objidl.idl";	// for IPersist (IFilter's root)

#include "devenum.idl"

// core interfaces
#include "axcore.idl"

// extended, ActiveMovie-only interfaces
#include "axextend.idl"

// application interfaces for DVD
#include "dvdif.idl"

// Dynamic graph stuff
#include "dyngraph.idl"


// Video Mixing Renderer
#include "VMRender.idl"


//
// Restore the previous setting for C4201 compiler warning
//
cpp_quote("// Restore the previous setting for C4201 compiler warning")
cpp_quote("#pragma warning(pop)")
